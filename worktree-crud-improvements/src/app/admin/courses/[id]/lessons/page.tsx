'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useParams } from 'next/navigation';
import { useEffect, useState, useCallback } from 'react';
import { toast } from 'sonner';

// Components
import {
  AdminPageLayout,
  FormCard,
  Button
} from '@/components/ui/modern';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';

// Icons
import { 
  Loader2, BookOpen, ArrowLeft, Save, RotateCcw, 
  ChevronLeft, ChevronRight, AlertTriangle,
  BookMarked, Zap, RefreshCw
} from 'lucide-react';

interface LessonTemplate {
  id?: string;
  lessonNumber: number;
  title: string;
  description?: string;
}

interface CourseWithTemplates {
  id: string;
  name: string;
  numberOfLessons: number | null;
  lessonTemplates: LessonTemplate[];
}

export default function CourseLessonsPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const params = useParams();
  const courseId = params.id as string;
  
  // State
  const [course, setCourse] = useState<CourseWithTemplates | null>(null);
  const [templates, setTemplates] = useState<LessonTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [currentLessonIndex, setCurrentLessonIndex] = useState(0);
  const [hasChanges, setHasChanges] = useState(false);

  // Data fetching
  const fetchCourseTemplates = useCallback(async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/courses/${courseId}/lesson-templates`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Curso não encontrado');
        }
        throw new Error('Falha ao carregar templates do curso');
      }

      const result = await response.json();
      const courseData = result.data;
      setCourse(courseData);

      // Initialize templates based on numberOfLessons
      if (courseData.numberOfLessons && courseData.numberOfLessons > 0) {
        const initialTemplates: LessonTemplate[] = [];
        for (let i = 1; i <= courseData.numberOfLessons; i++) {
          const existing = courseData.lessonTemplates.find((t: LessonTemplate) => t.lessonNumber === i);
          initialTemplates.push({
            lessonNumber: i,
            title: existing?.title || '', // Título em branco por padrão
            description: existing?.description || ''
          });
        }
        setTemplates(initialTemplates);
      } else {
        setTemplates([]);
      }

    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Erro ao carregar templates');
      router.push('/admin/courses');
    } finally {
      setIsLoading(false);
    }
  }, [courseId, router]);

  // Effects
  useEffect(() => {
    if (!loading && (!user || !['admin', 'super_admin'].includes(user.profile?.role || ''))) {
      router.push('/');
      return;
    }

    if (user && ['admin', 'super_admin'].includes(user.profile?.role || '')) {
      fetchCourseTemplates();
    }
  }, [user, loading, router, fetchCourseTemplates]);

  // Template management
  const updateCurrentTemplate = (field: 'title' | 'description', value: string) => {
    if (templates.length === 0) return;
    
    const updatedTemplates = templates.map((template, index) => 
      index === currentLessonIndex
        ? { ...template, [field]: value }
        : template
    );
    setTemplates(updatedTemplates);
    setHasChanges(true);
  };

  const resetCurrentTemplate = () => {
    if (templates.length === 0) return;
    
    const updatedTemplates = templates.map((template, index) => 
      index === currentLessonIndex
        ? { ...template, title: '', description: '' } // Reset para título vazio
        : template
    );
    setTemplates(updatedTemplates);
    setHasChanges(true);
  };

  // Navigation
  const goToNextLesson = () => {
    if (currentLessonIndex < templates.length - 1) {
      setCurrentLessonIndex(currentLessonIndex + 1);
    }
  };

  const goToPreviousLesson = () => {
    if (currentLessonIndex > 0) {
      setCurrentLessonIndex(currentLessonIndex - 1);
    }
  };

  const goToLesson = (index: number) => {
    setCurrentLessonIndex(index);
  };

  // Save templates
  const handleSave = async () => {
    try {
      setIsSaving(true);
      const token = localStorage.getItem('auth_token');

      if (!token) {
        toast.error('Sessão expirada. Faça login novamente.');
        router.push('/login');
        return;
      }

      const response = await fetch(`/api/courses/${courseId}/lesson-templates`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ templates }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Falha ao salvar templates');
      }

      toast.success('Templates salvos com sucesso!');
      setHasChanges(false);
      
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Erro ao salvar templates');
    } finally {
      setIsSaving(false);
    }
  };

  // Apply templates to existing lessons
  const handleApplyToLessons = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/courses/${courseId}/lesson-templates`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ action: 'apply-to-lessons' }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Falha ao aplicar templates');
      }

      const result = await response.json();
      toast.success(result.message);
      
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Erro ao aplicar templates');
    }
  };

  // Loading state
  if (loading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">Carregando templates...</p>
        </div>
      </div>
    );
  }

  // Permission check
  if (!user || !['admin', 'super_admin'].includes(user.profile?.role || '')) {
    return null;
  }

  // No course or no lessons
  if (!course || !course.numberOfLessons || templates.length === 0) {
    return (
      <AdminPageLayout
        title="Templates das Aulas"
        description="Configurar títulos personalizados das aulas"
        icon={BookMarked}
        backUrl="/admin/courses"
      >
        <Card>
          <CardContent className="text-center py-12">
            <AlertTriangle className="w-16 h-16 mx-auto mb-4 text-yellow-500" />
            <h3 className="text-xl font-semibold mb-2">
              {!course ? 'Curso não encontrado' : 'Nenhuma aula configurada'}
            </h3>
            <p className="text-gray-600 mb-6">
              {!course 
                ? 'O curso solicitado não foi encontrado.' 
                : 'Este curso não possui aulas configuradas. Defina o número de aulas nas configurações do curso.'
              }
            </p>
            <Button onClick={() => router.push('/admin/courses')}>
              Voltar para Cursos
            </Button>
          </CardContent>
        </Card>
      </AdminPageLayout>
    );
  }

  const currentTemplate = templates[currentLessonIndex];
  const isFirstLesson = currentLessonIndex === 0;
  const isLastLesson = currentLessonIndex === templates.length - 1;

  return (
    <AdminPageLayout
      title={`Templates - ${course.name}`}
      description="Configure títulos personalizados para as aulas do curso"
      icon={BookMarked}
      backUrl="/admin/courses"
    >
      <div className="space-y-6">
        {/* Course Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="w-5 h-5" />
              {course.name}
            </CardTitle>
            <CardDescription>
              {course.numberOfLessons} aulas configuradas
              {hasChanges && (
                <Badge variant="secondary" className="ml-2">
                  Alterações não salvas
                </Badge>
              )}
            </CardDescription>
          </CardHeader>
        </Card>

        {/* Navigation */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">
                Aula {currentTemplate.lessonNumber} de {templates.length}
              </h3>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPreviousLesson}
                  disabled={isFirstLesson}
                >
                  <ChevronLeft className="w-4 h-4" />
                  Anterior
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextLesson}
                  disabled={isLastLesson}
                >
                  Próxima
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Lesson Selector */}
            <div className="flex flex-wrap gap-2">
              {templates.map((template, index) => (
                <Button
                  key={template.lessonNumber}
                  variant={index === currentLessonIndex ? "default" : "outline"}
                  size="sm"
                  onClick={() => goToLesson(index)}
                  className={index === currentLessonIndex ? "bg-[#667eea] hover:bg-[#5a6fd8]" : ""}
                >
                  {template.lessonNumber}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Template Editor */}
        <FormCard title={`Editar Aula ${currentTemplate.lessonNumber}`} icon={BookMarked}>
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium mb-2">
                Título da Aula
              </label>
              <Input
                value={currentTemplate.title}
                onChange={(e) => updateCurrentTemplate('title', e.target.value)}
                placeholder={`Aula ${currentTemplate.lessonNumber}`}
                className="text-lg"
              />
              <p className="text-sm text-gray-500 mt-1">
                Deixe como "Aula {currentTemplate.lessonNumber}" para usar o título padrão
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Descrição (opcional)
              </label>
              <Textarea
                value={currentTemplate.description || ''}
                onChange={(e) => updateCurrentTemplate('description', e.target.value)}
                placeholder="Descrição da aula, objetivos de aprendizado, etc."
                rows={4}
              />
            </div>

            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={resetCurrentTemplate}
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Restaurar Padrão
              </Button>

              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleApplyToLessons}
                  className="border-green-500 text-green-600 hover:bg-green-50"
                >
                  <Zap className="w-4 h-4 mr-2" />
                  Aplicar a Aulas Existentes
                </Button>
                
                <Button
                  onClick={handleSave}
                  disabled={isSaving || !hasChanges}
                  className="bg-[#667eea] hover:bg-[#5a6fd8]"
                >
                  {isSaving ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="w-4 h-4 mr-2" />
                  )}
                  Salvar Templates
                </Button>
              </div>
            </div>
          </div>
        </FormCard>

        {/* Preview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RefreshCw className="w-5 h-5" />
              Preview dos Títulos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
              {templates.map((template, index) => (
                <div
                  key={template.lessonNumber}
                  className={`p-3 border rounded-lg cursor-pointer transition-all ${
                    index === currentLessonIndex
                      ? 'border-[#667eea] bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => goToLesson(index)}
                >
                  <div className="font-medium text-sm">
                    {template.title && template.title.trim() !== '' 
                      ? `#${template.lessonNumber} ${template.title}`
                      : `Aula ${template.lessonNumber}`
                    }
                  </div>
                  {template.description && (
                    <div className="text-xs text-gray-500 mt-1 line-clamp-2">
                      {template.description}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminPageLayout>
  );
}