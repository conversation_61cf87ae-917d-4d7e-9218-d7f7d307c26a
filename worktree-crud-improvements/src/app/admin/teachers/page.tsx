'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState, useCallback, Suspense } from 'react';
import { toast } from 'sonner';
import { isAdminOrSuperAdmin } from '@/lib/roles';
import { teachersService, Teacher } from '@/lib/services/teachers.service';
import { useAutoSave } from '@/hooks/useAutoSave';

// Components
import {
  AdminPageLayout,
  FormCard,
  ModernInput,
  ModernTextarea,
  ModernSelect,
  FormActions,
  Button,
  Badge
} from '@/components/ui/modern';
import { AutoSaveIndicator } from '@/components/ui/auto-save-indicator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useConfirmationDialog } from '@/components/ui/confirmation-dialog';

// Icons
import { 
  Loader2, UserCheck, ArrowLeft, Plus, Search, Edit, Trash2, Mail, Phone, 
  Save, GraduationCap, FileText, Activity, User
} from 'lucide-react';

interface TeacherFormData {
  name: string;
  email: string;
  phone: string;
  specialization: string;
  notes: string;
  status: string;
}

function TeachersPageContent() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get mode and ID from URL parameters
  const mode = searchParams.get('mode') || 'list'; // list, new, edit
  const teacherId = searchParams.get('id');
  
  // State
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [selectedTeacher, setSelectedTeacher] = useState<Teacher | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState<string | null>(null);
  const { showConfirmation, ConfirmationDialog } = useConfirmationDialog();
  
  const [formData, setFormData] = useState<TeacherFormData>({
    name: '',
    email: '',
    phone: '',
    specialization: '',
    notes: '',
    status: 'active'
  });

  // Navigation helpers
  const navigateToMode = useCallback((newMode: string, id?: string) => {
    const params = new URLSearchParams();
    params.set('mode', newMode);
    if (id) params.set('id', id);
    router.push(`/admin/teachers?${params.toString()}`);
  }, [router]);

  const navigateToList = useCallback(() => {
    router.push('/admin/teachers');
  }, [router]);

  // AutoSave function for edit mode
  const autoSaveTeacher = useCallback(async (data: TeacherFormData) => {
    if (!teacherId || mode !== 'edit') return;

    const payload = {
      name: data.name.trim(),
      email: data.email.trim() || undefined,
      phone: data.phone.trim() || undefined,
      specialization: data.specialization.trim() || undefined,
      notes: data.notes.trim() || undefined,
      status: data.status
    };

    const response = await teachersService.updateTeacher(teacherId, payload);

    if (!response.success) {
      throw new Error(response.error || 'Falha ao salvar professor');
    }

    return response;
  }, [teacherId, mode]);

  // AutoSave hook for edit mode
  const autoSaveState = useAutoSave({
    data: formData,
    onSave: autoSaveTeacher,
    config: { debounceMs: 2000, enableToast: false },
    isValid: (data) => !!data.name.trim(),
    enabled: mode === 'edit'
  });

  // Data fetching
  const fetchTeachers = useCallback(async (search?: string) => {
    try {
      if (mode === 'list') {
        setIsLoading(true);
      }
      setError(null);
      const response = await teachersService.getTeachers(search || '');
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch teachers');
      }

      setTeachers(response.data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      if (mode === 'list') {
        setIsLoading(false);
      }
    }
  }, [mode]);

  const fetchTeacher = useCallback(async (id: string) => {
    try {
      setIsLoading(true);
      const response = await teachersService.getTeacher(id);

      if (!response.success) {
        throw new Error(response.error || 'Falha ao carregar professor');
      }

      const teacherData = response.data;
      setSelectedTeacher(teacherData);

      // Populate form for editing
      setFormData({
        name: teacherData.name || '',
        email: teacherData.email || '',
        phone: teacherData.phone || '',
        specialization: teacherData.specialization || '',
        notes: teacherData.notes || '',
        status: teacherData.status || 'active'
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Effects
  useEffect(() => {
    if (!loading && !isAdminOrSuperAdmin(user)) {
      router.push('/');
      return;
    }

    if (isAdminOrSuperAdmin(user)) {
      if (mode === 'edit' && teacherId) {
        fetchTeacher(teacherId);
      } else if (mode === 'list') {
        fetchTeachers('');
      } else if (mode === 'new') {
        setIsLoading(false);
        // Reset form for new teacher
        setFormData({
          name: '',
          email: '',
          phone: '',
          specialization: '',
          notes: '',
          status: 'active'
        });
      }
    }
  }, [user, loading, router, mode, teacherId, fetchTeacher, fetchTeachers]);

  // Form handlers
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Nome é obrigatório');
      return;
    }

    try {
      setIsSubmitting(true);

      const payload = {
        name: formData.name.trim(),
        email: formData.email.trim() || undefined,
        phone: formData.phone.trim() || undefined,
        specialization: formData.specialization.trim() || undefined,
        notes: formData.notes.trim() || undefined,
        status: formData.status
      };

      if (mode === 'new') {
        const response = await teachersService.createTeacher(payload);

        if (!response.success) {
          throw new Error(response.error || 'Falha ao criar professor');
        }

        toast.success('Professor criado com sucesso!');
        navigateToList();
      } else if (mode === 'edit' && teacherId) {
        // In edit mode, just navigate since autosave handles saving
        navigateToList();
      }
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Erro ao salvar professor');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSearch = useCallback(() => {
    fetchTeachers(searchTerm);
  }, [fetchTeachers, searchTerm]);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (mode === 'list') {
        fetchTeachers(searchTerm);
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchTerm, mode, fetchTeachers]);

  const handleDelete = (teacherId: string, teacherName: string) => {
    showConfirmation({
      title: 'Excluir Professor',
      description: `Tem certeza que deseja excluir o professor "${teacherName}"? Esta ação não pode ser desfeita.`,
      confirmText: 'Excluir',
      cancelText: 'Cancelar',
      variant: 'destructive',
      icon: 'delete',
      onConfirm: async () => {
        try {
          const response = await teachersService.deleteTeacher(teacherId);

          if (!response.success) {
            throw new Error(response.error || 'Failed to delete teacher');
          }

          toast.success('Professor excluído com sucesso!');
          fetchTeachers(searchTerm);
        } catch (err) {
          toast.error(err instanceof Error ? err.message : 'Erro ao excluir professor');
        }
      }
    });
  };

  // Utility functions
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'secondary';
      default:
        return 'secondary';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Ativo';
      case 'inactive':
        return 'Inativo';
      default:
        return status;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  // Loading state - only show full page loading for non-list modes
  if (loading || (isLoading && mode !== 'list')) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">
            {mode === 'edit' ? 'Carregando professor...' : 'Carregando professores...'}
          </p>
        </div>
      </div>
    );
  }

  // Permission check
  if (!isAdminOrSuperAdmin(user)) {
    return null;
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-destructive">Erro</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
            <Button onClick={() => navigateToList()} className="mt-4">
              Voltar para Lista
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render based on mode
  if (mode === 'new') {
    return (
      <AdminPageLayout
        title="Novo Professor"
        description="Cadastrar um novo professor no sistema"
        icon={UserCheck}
        backUrl="/admin/teachers"
      >
        <FormCard title="Cadastrar Novo Professor" icon={UserCheck}>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 xl:grid-cols-3">
              <ModernInput
                label="Nome"
                value={formData.name}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Nome completo do professor"
                icon={User}
                required
                className="md:col-span-2 xl:col-span-1"
              />

              <ModernInput
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, email: e.target.value })}
                placeholder="<EMAIL>"
                icon={Mail}
              />

              <ModernInput
                label="Telefone"
                value={formData.phone}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, phone: e.target.value })}
                placeholder="(11) 99999-9999"
                icon={Phone}
              />

              <ModernInput
                label="Especialização"
                value={formData.specialization}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, specialization: e.target.value })}
                placeholder="Ex: Matemática, Física, Programação..."
                icon={GraduationCap}
                className="md:col-span-2 xl:col-span-1"
              />
            </div>

            <ModernTextarea
              label="Observações"
              value={formData.notes}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setFormData({ ...formData, notes: e.target.value })}
              placeholder="Observações sobre o professor..."
              rows={3}
              icon={FileText}
            />

            <FormActions
              onCancel={() => navigateToList()}
              isLoading={isSubmitting}
              submitLabel="Salvar Professor"
              cancelLabel="Cancelar"
              submitIcon={Save}
            />
          </form>
        </FormCard>
      </AdminPageLayout>
    );
  }

  if (mode === 'edit' && selectedTeacher) {
    return (
      <AdminPageLayout
        title="Editar Professor"
        description={`Edite as informações do professor ${selectedTeacher.name}`}
        icon={UserCheck}
        backUrl="/admin/teachers"
      >
        <FormCard 
          title="Informações do Professor" 
          icon={UserCheck}
          rightContent={
            <AutoSaveIndicator
              isAutoSaving={autoSaveState.isAutoSaving}
              lastSaved={autoSaveState.lastSaved}
              hasUnsavedChanges={autoSaveState.hasUnsavedChanges}
              error={autoSaveState.error}
            />
          }
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 xl:grid-cols-3">
              <ModernInput
                label="Nome"
                value={formData.name}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Nome completo do professor"
                icon={User}
                required
                className="md:col-span-2 xl:col-span-1"
              />

              <ModernSelect
                label="Status"
                value={formData.status}
                onValueChange={(value) => setFormData({ ...formData, status: value })}
                icon={Activity}
                options={[
                  { value: 'active', label: 'Ativo' },
                  { value: 'inactive', label: 'Inativo' }
                ]}
              />

              <ModernInput
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, email: e.target.value })}
                placeholder="<EMAIL>"
                icon={Mail}
              />

              <ModernInput
                label="Telefone"
                value={formData.phone}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, phone: e.target.value })}
                placeholder="(11) 99999-9999"
                icon={Phone}
              />

              <ModernInput
                label="Especialização"
                value={formData.specialization}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, specialization: e.target.value })}
                placeholder="Ex: Matemática, Física, Programação..."
                icon={GraduationCap}
                className="md:col-span-2 xl:col-span-1"
              />
            </div>

            <ModernTextarea
              label="Observações"
              value={formData.notes}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setFormData({ ...formData, notes: e.target.value })}
              placeholder="Observações sobre o professor..."
              rows={3}
              icon={FileText}
            />

            <div className="flex justify-end">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigateToList()}
                className="flex items-center"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar para Lista
              </Button>
            </div>
          </form>
        </FormCard>
      </AdminPageLayout>
    );
  }

  // Default list view
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="w-full max-w-[1600px] mx-auto">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-2xl p-8 text-white shadow-xl mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button onClick={() => router.push('/')} variant="outline" size="sm" className="bg-white/20 text-white border-white/30 hover:bg-white/30">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar
              </Button>
              <div>
                <h1 className="text-3xl font-bold mb-2 flex items-center">
                  <UserCheck className="w-8 h-8 mr-3" />
                  Gerenciar Professores
                </h1>
                <p className="text-white/90">
                  Administre informações dos professores do sistema
                </p>
              </div>
            </div>
            <div className="hidden md:block">
              <Button 
                onClick={() => navigateToMode('new')}
                className="bg-white/20 text-white border-white/30 hover:bg-white/30 backdrop-blur-sm"
                size="lg"
              >
                <Plus className="w-5 h-5 mr-2" />
                Novo Professor
              </Button>
            </div>
          </div>
          {/* Mobile button */}
          <div className="md:hidden mt-4">
            <Button 
              onClick={() => navigateToMode('new')}
              className="bg-white/20 text-white border-white/30 hover:bg-white/30 backdrop-blur-sm w-full"
            >
              <Plus className="w-5 h-5 mr-2" />
              Novo Professor
            </Button>
          </div>
        </div>

        {/* Search Section */}
        <Card className="mb-6 shadow-lg border-0">
          <CardContent className="pt-6">
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Buscar professores por nome, email, telefone ou especialização..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  className="border-2 border-gray-200 focus:border-[#667eea] focus:ring-4 focus:ring-[#667eea]/20 transition-all duration-300"
                />
              </div>
              <Button 
                onClick={handleSearch}
                className="bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:from-[#5a6fd8] hover:to-[#6b4190] text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
              >
                <Search className="w-4 h-4 mr-2" />
                Buscar
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Teachers Table */}
        <Card className="shadow-xl border-0">
          <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
            <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
              <UserCheck className="w-6 h-6 mr-2 text-[#667eea]" />
              Professores Cadastrados
            </CardTitle>
            <CardDescription className="text-lg">
              {teachers.length} professor{teachers.length !== 1 ? 'es' : ''} encontrado{teachers.length !== 1 ? 's' : ''}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading && mode === 'list' ? (
              <div className="text-center py-12">
                <Loader2 className="mx-auto animate-spin" size={32} />
                <p className="mt-4 text-muted-foreground">Carregando professores...</p>
              </div>
            ) : teachers.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-20 h-20 bg-gradient-to-br from-[#667eea] to-[#764ba2] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl">
                  <UserCheck className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  Nenhum professor encontrado
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  {searchTerm ? 'Tente uma busca diferente ou ajuste os filtros.' : 'Comece cadastrando um novo professor para o sistema.'}
                </p>
                {!searchTerm && (
                  <Button 
                    onClick={() => navigateToMode('new')} 
                    className="bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:from-[#5a6fd8] hover:to-[#6b4190] text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
                    size="lg"
                  >
                    <Plus className="w-5 h-5 mr-2" />
                    Cadastrar Primeiro Professor
                  </Button>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Professor</TableHead>
                      <TableHead>Contato</TableHead>
                      <TableHead>Especialização</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Turmas/Aulas</TableHead>
                      <TableHead>Cadastrado em</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {teachers.map((teacher) => (
                      <TableRow key={teacher.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{teacher.name}</div>
                            {teacher.notes && (
                              <div className="text-sm text-muted-foreground">
                                {teacher.notes}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {teacher.email && (
                              <div className="flex items-center text-sm">
                                <Mail className="w-3 h-3 mr-1" />
                                {teacher.email}
                              </div>
                            )}
                            {teacher.phone && (
                              <div className="flex items-center text-sm">
                                <Phone className="w-3 h-3 mr-1" />
                                {teacher.phone}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {teacher.specialization ? (
                            <div className="flex items-center">
                              <GraduationCap className="w-4 h-4 mr-1 text-[#667eea]" />
                              {teacher.specialization}
                            </div>
                          ) : (
                            <span className="text-muted-foreground">Não informado</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <Badge variant={getStatusBadgeVariant(teacher.status)}>
                            {getStatusLabel(teacher.status)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div>{teacher._count?.classes || 0} turma{(teacher._count?.classes || 0) !== 1 ? 's' : ''}</div>
                            <div className="text-muted-foreground">{teacher._count?.lessons || 0} aula{(teacher._count?.lessons || 0) !== 1 ? 's' : ''}</div>
                          </div>
                        </TableCell>
                        <TableCell>{formatDate(teacher.createdAt)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              onClick={() => navigateToMode('edit', teacher.id)}
                              variant="outline"
                              size="sm"
                              className="border-[#667eea] text-[#667eea] hover:bg-[#667eea] hover:text-white transition-all duration-300"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              onClick={() => handleDelete(teacher.id, teacher.name)}
                              variant="outline"
                              size="sm"
                              className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white transition-all duration-300"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      <ConfirmationDialog />
    </div>
  );
}

export default function TeachersPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">Carregando...</p>
        </div>
      </div>
    }>
      <TeachersPageContent />
    </Suspense>
  );
}