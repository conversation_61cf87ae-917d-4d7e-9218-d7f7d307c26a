'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState, useCallback, Suspense } from 'react';
import { toast } from 'sonner';
import { isAdminOrSuperAdmin } from '@/lib/roles';

// Components
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useConfirmationDialog } from '@/components/ui/confirmation-dialog';

// Icons
import { 
  Loader2, Calendar, Clock, User, X, CheckCircle,
  CalendarDays, ArrowLeft, Search, Filter, RefreshCw
} from 'lucide-react';

interface MentoringBooking {
  id: string;
  slotId: string;
  studentId: string;
  scheduledDateTime: string;
  status: 'confirmed' | 'cancelled' | 'completed';
  studentMessage?: string;
  courseContext?: string;
  cancellationReason?: string;
  createdAt: string;
  updatedAt: string;
  slot: {
    id: string;
    teacher: {
      id: string;
      name: string;
      email: string;
    };
    dayOfWeek: string;
    startTime: string;
    endTime: string;
    duration: number;
  };
  student: {
    id: string;
    name: string;
    email: string;
  };
}

interface Filters {
  status: string;
  teacherId: string;
  studentId: string;
  fromDate: string;
  toDate: string;
}

function MentoringBookingsPageContent() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // State
  const [bookings, setBookings] = useState<MentoringBooking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState<MentoringBooking | null>(null);
  const [cancellationReason, setCancellationReason] = useState('');
  const [showCancelModal, setShowCancelModal] = useState(false);
  const { showConfirmation, ConfirmationDialog } = useConfirmationDialog();

  const [filters, setFilters] = useState<Filters>({
    status: searchParams.get('status') || '',
    teacherId: searchParams.get('teacherId') || '',
    studentId: searchParams.get('studentId') || '',
    fromDate: searchParams.get('fromDate') || '',
    toDate: searchParams.get('toDate') || ''
  });

  // Data fetching
  const fetchBookings = useCallback(async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const params = new URLSearchParams();
      if (filters.status) params.set('status', filters.status);
      if (filters.teacherId) params.set('teacherId', filters.teacherId);
      if (filters.studentId) params.set('studentId', filters.studentId);
      if (filters.fromDate) params.set('fromDate', filters.fromDate);
      if (filters.toDate) params.set('toDate', filters.toDate);

      const url = `/api/mentoring/bookings?${params.toString()}`;
      const response = await fetch(url, { headers });

      if (!response.ok) {
        throw new Error('Falha ao carregar agendamentos');
      }

      const result = await response.json();
      setBookings(result.data || []);
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Erro ao carregar agendamentos');
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  // Effects
  useEffect(() => {
    if (!loading && !isAdminOrSuperAdmin(user)) {
      router.push('/');
      return;
    }

    if (isAdminOrSuperAdmin(user)) {
      fetchBookings();
    }
  }, [user, loading, router, fetchBookings]);

  // Handlers
  const handleCancelBooking = (booking: MentoringBooking) => {
    setSelectedBooking(booking);
    setCancellationReason('');
    setShowCancelModal(true);
  };

  const confirmCancelBooking = async () => {
    if (!selectedBooking || !cancellationReason.trim()) {
      toast.error('Por favor, informe o motivo do cancelamento');
      return;
    }

    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        toast.error('Sessão expirada');
        return;
      }

      const response = await fetch(`/api/mentoring/bookings/${selectedBooking.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          action: 'cancel',
          reason: cancellationReason.trim()
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Falha ao cancelar agendamento');
      }

      toast.success('Agendamento cancelado com sucesso!');
      setShowCancelModal(false);
      setSelectedBooking(null);
      setCancellationReason('');
      fetchBookings();
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Erro ao cancelar agendamento');
    }
  };

  const handleMarkAsCompleted = (booking: MentoringBooking) => {
    showConfirmation({
      title: 'Marcar como Concluída',
      description: `Tem certeza que deseja marcar a mentoria de ${booking.student.name} com ${booking.slot.teacher.name} como concluída?`,
      confirmText: 'Confirmar',
      cancelText: 'Cancelar',
      variant: 'default',
      icon: 'check',
      onConfirm: async () => {
        try {
          const token = localStorage.getItem('auth_token');
          if (!token) {
            toast.error('Sessão expirada');
            return;
          }

          const response = await fetch(`/api/mentoring/bookings/${booking.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              action: 'complete'
            }),
          });

          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Falha ao marcar como concluída');
          }

          toast.success('Mentoria marcada como concluída!');
          fetchBookings();
        } catch (err) {
          toast.error(err instanceof Error ? err.message : 'Erro ao marcar como concluída');
        }
      }
    });
  };

  // Utility functions
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Confirmada</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">Cancelada</Badge>;
      case 'completed':
        return <Badge variant="success" className="bg-green-100 text-green-800">Concluída</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDateTime = (dateTime: string) => {
    const date = new Date(dateTime);
    return {
      date: date.toLocaleDateString('pt-BR', {
        weekday: 'short',
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }),
      time: date.toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
      })
    };
  };

  const filteredBookings = bookings.filter(booking => {
    if (!searchTerm) return true;
    const search = searchTerm.toLowerCase();
    return (
      booking.student.name.toLowerCase().includes(search) ||
      booking.slot.teacher.name.toLowerCase().includes(search) ||
      booking.courseContext?.toLowerCase().includes(search)
    );
  });

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">Carregando...</p>
        </div>
      </div>
    );
  }

  // Permission check
  if (!isAdminOrSuperAdmin(user)) {
    return null;
  }

  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
        <div className="w-full max-w-[1600px] mx-auto">
          {/* Header */}
          <div className="bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-2xl p-8 text-white shadow-xl mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button 
                  onClick={() => router.push('/admin')} 
                  variant="outline" 
                  size="sm" 
                  className="bg-white/20 text-white border-white/30 hover:bg-white/30"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Voltar
                </Button>
                <div>
                  <h1 className="text-3xl font-bold mb-2 flex items-center">
                    <CalendarDays className="w-8 h-8 mr-3" />
                    Agendamentos de Mentoria
                  </h1>
                  <p className="text-white/90">
                    Gerencie todos os agendamentos de mentoria
                  </p>
                </div>
              </div>
              <div className="hidden md:flex space-x-3">
                <Button 
                  onClick={() => setShowFilters(!showFilters)}
                  variant="outline"
                  className="bg-white/20 text-white border-white/30 hover:bg-white/30"
                >
                  <Filter className="w-4 h-4 mr-2" />
                  Filtros
                </Button>
                <Button 
                  onClick={fetchBookings}
                  variant="outline"
                  className="bg-white/20 text-white border-white/30 hover:bg-white/30"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Atualizar
                </Button>
              </div>
            </div>
          </div>

          {/* Filters */}
          {showFilters && (
            <Card className="mb-6 shadow-lg border-0">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Filter className="w-5 h-5 mr-2" />
                  Filtros
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                  <Select value={filters.status} onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Todos</SelectItem>
                      <SelectItem value="confirmed">Confirmada</SelectItem>
                      <SelectItem value="cancelled">Cancelada</SelectItem>
                      <SelectItem value="completed">Concluída</SelectItem>
                    </SelectContent>
                  </Select>

                  <div>
                    <Input
                      type="date"
                      placeholder="Data inicial"
                      value={filters.fromDate}
                      onChange={(e) => setFilters(prev => ({ ...prev, fromDate: e.target.value }))}
                    />
                  </div>

                  <div>
                    <Input
                      type="date"
                      placeholder="Data final"
                      value={filters.toDate}
                      onChange={(e) => setFilters(prev => ({ ...prev, toDate: e.target.value }))}
                    />
                  </div>

                  <Button 
                    onClick={fetchBookings}
                    className="bg-gradient-to-r from-[#667eea] to-[#764ba2]"
                  >
                    Aplicar Filtros
                  </Button>

                  <Button 
                    onClick={() => {
                      setFilters({
                        status: '',
                        teacherId: '',
                        studentId: '',
                        fromDate: '',
                        toDate: ''
                      });
                      fetchBookings();
                    }}
                    variant="outline"
                  >
                    Limpar
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Search */}
          <Card className="mb-6 shadow-lg border-0">
            <CardContent className="pt-6">
              <div className="flex gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Buscar por aluno, professor ou contexto..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="border-2 border-gray-200 focus:border-[#667eea] focus:ring-4 focus:ring-[#667eea]/20 transition-all duration-300"
                  />
                </div>
                <Button 
                  onClick={fetchBookings}
                  className="bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:from-[#5a6fd8] hover:to-[#6b4190] text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
                >
                  <Search className="w-4 h-4 mr-2" />
                  Buscar
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Bookings Table */}
          <Card className="shadow-xl border-0">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
              <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
                <CalendarDays className="w-6 h-6 mr-2 text-[#667eea]" />
                Agendamentos ({filteredBookings.length})
              </CardTitle>
              <CardDescription className="text-lg">
                Visualize e gerencie todos os agendamentos de mentoria
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="text-center py-12">
                  <Loader2 className="mx-auto animate-spin" size={32} />
                  <p className="mt-4 text-muted-foreground">Carregando agendamentos...</p>
                </div>
              ) : filteredBookings.length === 0 ? (
                <div className="text-center py-12">
                  <div className="w-20 h-20 bg-gradient-to-br from-[#667eea] to-[#764ba2] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl">
                    <CalendarDays className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    Nenhum agendamento encontrado
                  </h3>
                  <p className="text-gray-600 mb-6 max-w-md mx-auto">
                    {searchTerm || Object.values(filters).some(f => f) ? 'Tente ajustar os filtros ou termos de busca.' : 'Aguarde novos agendamentos de mentoria.'}
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Aluno</TableHead>
                        <TableHead>Professor</TableHead>
                        <TableHead>Data & Horário</TableHead>
                        <TableHead>Duração</TableHead>
                        <TableHead>Contexto</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Ações</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredBookings.map((booking) => {
                        const dateTime = formatDateTime(booking.scheduledDateTime);
                        return (
                          <TableRow key={booking.id}>
                            <TableCell>
                              <div className="flex items-center">
                                <User className="w-4 h-4 mr-2 text-[#667eea]" />
                                <div>
                                  <div className="font-medium">{booking.student.name}</div>
                                  <div className="text-sm text-muted-foreground">{booking.student.email}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <User className="w-4 h-4 mr-2 text-[#667eea]" />
                                <div>
                                  <div className="font-medium">{booking.slot.teacher.name}</div>
                                  <div className="text-sm text-muted-foreground">{booking.slot.teacher.email}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <Calendar className="w-4 h-4 mr-2" />
                                <div>
                                  <div className="font-medium">{dateTime.date}</div>
                                  <div className="text-sm text-muted-foreground flex items-center">
                                    <Clock className="w-3 h-3 mr-1" />
                                    {dateTime.time}
                                  </div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>{booking.slot.duration} min</TableCell>
                            <TableCell>
                              <div className="max-w-xs">
                                {booking.courseContext && (
                                  <div className="text-sm font-medium truncate">{booking.courseContext}</div>
                                )}
                                {booking.studentMessage && (
                                  <div className="text-xs text-muted-foreground truncate">{booking.studentMessage}</div>
                                )}
                                {booking.cancellationReason && (
                                  <div className="text-xs text-red-600 truncate">Motivo: {booking.cancellationReason}</div>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              {getStatusBadge(booking.status)}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end space-x-2">
                                {booking.status === 'confirmed' && (
                                  <>
                                    <Button
                                      onClick={() => handleMarkAsCompleted(booking)}
                                      variant="outline"
                                      size="sm"
                                      className="border-green-500 text-green-500 hover:bg-green-500 hover:text-white transition-all duration-300"
                                    >
                                      <CheckCircle className="w-4 h-4" />
                                    </Button>
                                    <Button
                                      onClick={() => handleCancelBooking(booking)}
                                      variant="outline"
                                      size="sm"
                                      className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white transition-all duration-300"
                                    >
                                      <X className="w-4 h-4" />
                                    </Button>
                                  </>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Cancel Modal */}
      {showCancelModal && selectedBooking && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="flex items-center text-red-600">
                <X className="w-5 h-5 mr-2" />
                Cancelar Agendamento
              </CardTitle>
              <CardDescription>
                Cancelar mentoria de {selectedBooking.student.name} com {selectedBooking.slot.teacher.name}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Motivo do Cancelamento *</label>
                <Textarea
                  placeholder="Informe o motivo do cancelamento..."
                  value={cancellationReason}
                  onChange={(e) => setCancellationReason(e.target.value)}
                  rows={3}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowCancelModal(false);
                    setSelectedBooking(null);
                    setCancellationReason('');
                  }}
                >
                  Cancelar
                </Button>
                <Button
                  onClick={confirmCancelBooking}
                  variant="destructive"
                  disabled={!cancellationReason.trim()}
                >
                  Confirmar Cancelamento
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <ConfirmationDialog />
    </>
  );
}

export default function MentoringBookingsPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">Carregando...</p>
        </div>
      </div>
    }>
      <MentoringBookingsPageContent />
    </Suspense>
  );
}