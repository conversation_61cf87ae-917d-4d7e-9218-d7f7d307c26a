'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState, useCallback, Suspense, Fragment } from 'react';
import { toast } from 'sonner';
import { canManageClasses } from '@/lib/roles';
import { formatDateForDisplay } from '@/lib/date-utils';
import { getDefaultLessonDuration } from '@/lib/time-utils';
import { useAutoSave } from '@/hooks/useAutoSave';

// Components
import {
  AdminPageLayout,
  FormCard,
  ModernInput,
  ModernTextarea,
  ModernSelect,
  FormActions,
  Button,
  Badge
} from '@/components/ui/modern';
import { AutoSaveIndicator } from '@/components/ui/auto-save-indicator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { Switch } from '@/components/ui/switch';

// Icons
import { 
  Loader2, Calendar, ArrowLeft, Plus, Search, Edit, Trash2, Users, BookOpen, 
  Save, Clock, FileText, CalendarPlus, ChevronDown, ChevronRight,
  Mail, Phone
} from 'lucide-react';

interface Class {
  id: string;
  name: string;
  description?: string;
  startDate: string;
  endDate?: string;
  isActive: boolean;
  maxStudents?: number;
  course: {
    id: string;
    name: string;
    lessonDuration?: number;
  };
  _count: {
    enrollments: number;
    lessons: number;
  };
}

interface Course {
  id: string;
  name: string;
  numberOfLessons?: number;
  lessonDuration?: number;
}

interface Student {
  id: string;
  name: string;
  email: string | null;
  phone: string | null;
  status: string;
}

interface Lesson {
  id: string;
  title: string;
  description?: string;
  scheduledDate: string;
  duration: number;
  location?: string;
  notes?: string;
  _count: {
    attendance: number;
  };
  attendanceStats?: {
    present: number;
    absent: number;
    makeup: number;
  };
}

interface ClassFormData {
  name: string;
  description: string;
  courseId: string;
  startDate: string;
  endDate: string;
  maxStudents: string;
  isActive: boolean;
}

function ClassesPageContent() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get mode and ID from URL parameters
  const mode = searchParams.get('mode') || 'list'; // list, new, edit
  const classId = searchParams.get('id');
  
  // State
  const [classes, setClasses] = useState<Class[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [selectedClass, setSelectedClass] = useState<Class | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState<string | null>(null);
  const { showConfirmation, ConfirmationDialog } = useConfirmationDialog();
  const [expandedClasses, setExpandedClasses] = useState<Set<string>>(new Set());
  const [classStudents, setClassStudents] = useState<Record<string, Student[]>>({});
  const [classLessons, setClassLessons] = useState<Record<string, Lesson[]>>({});
  const [loadingStudents, setLoadingStudents] = useState<Set<string>>(new Set());
  const [loadingLessons, setLoadingLessons] = useState<Set<string>>(new Set());
  
  const [formData, setFormData] = useState<ClassFormData>({
    name: '',
    description: '',
    courseId: '',
    startDate: '',
    endDate: '',
    maxStudents: '',
    isActive: true
  });

  // Calculate end date based on start date and number of lessons
  // Uses same logic as generateLessonsForClass from lesson-utils.ts
  const calculateEndDate = useCallback((startDate: string, courseId: string) => {
    if (!startDate || !courseId) return '';
    
    const selectedCourse = courses.find(course => course.id === courseId);
    if (!selectedCourse?.numberOfLessons) return '';
    
    // Create a local date to avoid timezone issues (same as lesson-utils.ts)
    const start = new Date(startDate);
    const localStartDate = new Date(start.getFullYear(), start.getMonth(), start.getDate());
    
    // Calculate the date for the last lesson (start date + (numberOfLessons - 1) weeks)
    const lastLessonDate = new Date(localStartDate);
    lastLessonDate.setDate(localStartDate.getDate() + ((selectedCourse.numberOfLessons - 1) * 7));
    
    return lastLessonDate.toISOString().split('T')[0];
  }, [courses]);

  // Navigation helpers
  const navigateToMode = useCallback((newMode: string, id?: string) => {
    const params = new URLSearchParams();
    params.set('mode', newMode);
    if (id) params.set('id', id);
    router.push(`/admin/classes?${params.toString()}`);
  }, [router]);

  const navigateToList = useCallback(() => {
    router.push('/admin/classes');
  }, [router]);

  // Data fetching
  const fetchClasses = useCallback(async (search?: string) => {
    try {
      // Only show loading for table content, not full page
      if (mode === 'list') {
        setIsLoading(true);
      }
      setError(null);
      const url = search
        ? `/api/classes?search=${encodeURIComponent(search)}`
        : '/api/classes';

      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(url, { headers });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch classes');
      }

      const result = await response.json();
      setClasses(result.data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      // Only hide loading for table content
      if (mode === 'list') {
        setIsLoading(false);
      }
    }
  }, [mode]);

  const fetchCourses = useCallback(async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch('/api/courses', { headers });

      if (!response.ok) {
        throw new Error('Failed to fetch courses');
      }

      const result = await response.json();
      setCourses(result.data || []);
    } catch (err) {
      console.error('Error fetching courses:', err);
    }
  }, []);

  const fetchClass = useCallback(async (id: string) => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`/api/classes/${id}`, { headers });

      if (!response.ok) {
        throw new Error('Falha ao carregar turma');
      }

      const result = await response.json();
      const classData = result.data;
      setSelectedClass(classData);

      // Populate form for editing
      setFormData({
        name: classData.name || '',
        description: classData.description || '',
        courseId: classData.course?.id || '',
        startDate: classData.startDate ? classData.startDate.split('T')[0] : '',
        endDate: classData.endDate ? classData.endDate.split('T')[0] : '',
        maxStudents: classData.maxStudents ? classData.maxStudents.toString() : '',
        isActive: classData.isActive !== false
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchClassStudents = useCallback(async (classId: string) => {
    if (classStudents[classId]) {
      return; // Already loaded
    }

    setLoadingStudents(prev => new Set(prev).add(classId));
    
    try {
      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`/api/enrollments?classId=${classId}&status=active`, { headers });

      if (!response.ok) {
        throw new Error('Failed to fetch students');
      }

      const result = await response.json();
      // Extract students from enrollments
      const students = result.data?.map((enrollment: { student: Student }) => enrollment.student) || [];
      setClassStudents(prev => ({
        ...prev,
        [classId]: students
      }));
    } catch (err) {
      console.error('Error fetching class students:', err);
    } finally {
      setLoadingStudents(prev => {
        const newSet = new Set(prev);
        newSet.delete(classId);
        return newSet;
      });
    }
  }, [classStudents]);

  const fetchClassLessons = useCallback(async (classId: string) => {
    if (classLessons[classId]) {
      return; // Already loaded
    }

    setLoadingLessons(prev => new Set(prev).add(classId));
    
    try {
      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`/api/lessons?classId=${classId}`, { headers });

      if (!response.ok) {
        throw new Error('Failed to fetch lessons');
      }

      const result = await response.json();
      setClassLessons(prev => ({
        ...prev,
        [classId]: result.data || []
      }));

      // Auto-scroll to current lesson after data is loaded
      setTimeout(() => {
        scrollToCurrentLesson(classId, result.data || []);
      }, 100);
    } catch (err) {
      console.error('Error fetching class lessons:', err);
    } finally {
      setLoadingLessons(prev => {
        const newSet = new Set(prev);
        newSet.delete(classId);
        return newSet;
      });
    }
  }, [classLessons]);

  // Function to scroll to the current lesson
  const scrollToCurrentLesson = useCallback((classId: string, lessons: Lesson[]) => {
    if (!lessons || lessons.length === 0) return;

    const now = new Date();
    
    // Find the current lesson: the next upcoming lesson or the most recent lesson if all are past
    let currentLessonIndex = -1;
    
    // First, try to find the next upcoming lesson
    for (let i = 0; i < lessons.length; i++) {
      const lessonDate = new Date(lessons[i].scheduledDate);
      if (lessonDate >= now) {
        currentLessonIndex = i;
        break;
      }
    }
    
    // If no upcoming lessons, use the last lesson
    if (currentLessonIndex === -1 && lessons.length > 0) {
      currentLessonIndex = lessons.length - 1;
    }
    
    // If we found a current lesson, scroll to it
    if (currentLessonIndex >= 0) {
      const lessonElement = document.querySelector(`[data-lesson-id="${lessons[currentLessonIndex].id}"]`);
      if (lessonElement && lessonElement.parentElement) {
        lessonElement.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center' 
        });
      }
    }
  }, []);

  // Effects
  useEffect(() => {
    if (!loading && !user) {
      router.push('/');
      return;
    }

    if (user) {
      if (mode === 'edit' && classId) {
        fetchClass(classId);
        fetchCourses();
      } else if (mode === 'list') {
        fetchClasses('');
      } else if (mode === 'new') {
        setIsLoading(false);
        fetchCourses();
        // Reset form for new class
        setFormData({
          name: '',
          description: '',
          courseId: '',
          startDate: '',
          endDate: '',
          maxStudents: '',
          isActive: true
        });
      }
    }
  }, [user, loading, router, mode, classId, fetchClass, fetchClasses, fetchCourses]);

  // AutoSave function for edit mode
  const autoSaveClass = useCallback(async (data: ClassFormData) => {
    if (!classId || mode !== 'edit') return;

    const token = localStorage.getItem('auth_token');
    if (!token) {
      throw new Error('Sessão expirada');
    }

    const payload = {
      name: data.name.trim(),
      description: data.description.trim() || null,
      courseId: data.courseId,
      startDate: data.startDate || null,
      endDate: data.endDate || null,
      maxStudents: data.maxStudents ? parseInt(data.maxStudents) : null,
      isActive: data.isActive
    };

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };

    const response = await fetch(`/api/classes/${classId}`, {
      method: 'PUT',
      headers,
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Falha ao salvar turma');
    }

    return response.json();
  }, [classId, mode]);

  // AutoSave hook for edit mode
  const autoSaveState = useAutoSave({
    data: formData,
    onSave: autoSaveClass,
    config: { debounceMs: 2000, enableToast: false },
    isValid: (data) => !!data.name.trim() && !!data.courseId,
    enabled: mode === 'edit'
  });

  // Form handlers
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Nome é obrigatório');
      return;
    }

    if (!formData.courseId) {
      toast.error('Curso é obrigatório');
      return;
    }

    try {
      setIsSubmitting(true);
      const token = localStorage.getItem('auth_token');

      if (!token) {
        toast.error('Sessão expirada. Faça login novamente.');
        router.push('/login');
        return;
      }

      const payload = {
        name: formData.name.trim(),
        description: formData.description.trim() || null,
        courseId: formData.courseId,
        startDate: formData.startDate || null,
        endDate: formData.endDate || null,
        maxStudents: formData.maxStudents ? parseInt(formData.maxStudents) : null,
        isActive: formData.isActive
      };

      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      if (mode === 'new') {
        const response = await fetch('/api/classes', {
          method: 'POST',
          headers,
          body: JSON.stringify(payload),
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Falha ao criar turma');
        }

        toast.success('Turma criada com sucesso!');
        navigateToList();
      } else if (mode === 'edit' && classId) {
        // In edit mode, just navigate since autosave handles saving
        navigateToList();
      }
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Erro ao salvar turma');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSearch = useCallback(() => {
    fetchClasses(searchTerm);
  }, [fetchClasses, searchTerm]);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (mode === 'list') {
        fetchClasses(searchTerm);
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchTerm, mode, fetchClasses]);

  const handleDelete = (classId: string, className: string) => {
    showConfirmation({
      title: 'Excluir Turma',
      description: `Tem certeza que deseja excluir a turma "${className}"? Esta ação não pode ser desfeita.`,
      confirmText: 'Excluir',
      cancelText: 'Cancelar',
      variant: 'destructive',
      icon: 'delete',
      onConfirm: async () => {
        try {
          const token = localStorage.getItem('auth_token');

          if (!token) {
            toast.error('Sessão expirada. Faça login novamente.');
            router.push('/login');
            return;
          }

          const headers: HeadersInit = {};
          if (token) {
            headers['Authorization'] = `Bearer ${token}`;
          }

          const response = await fetch(`/api/classes/${classId}`, {
            method: 'DELETE',
            headers
          });

          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Falha ao excluir turma');
          }

          toast.success('Turma excluída com sucesso!');
          fetchClasses(searchTerm);
        } catch (err) {
          toast.error(err instanceof Error ? err.message : 'Erro ao excluir turma');
        }
      }
    });
  };

  const toggleClassExpansion = (classId: string) => {
    setExpandedClasses(prev => {
      const newSet = new Set(prev);
      if (newSet.has(classId)) {
        newSet.delete(classId);
      } else {
        newSet.add(classId);
        fetchClassStudents(classId);
        fetchClassLessons(classId);
      }
      return newSet;
    });
  };

  // Utility functions for lessons
  const isLessonPast = (scheduledDate: string) => {
    return new Date(scheduledDate) < new Date();
  };

  const formatLessonDateTime = (scheduledDate: string, duration: number, course?: { lessonDuration?: number }) => {
    const date = new Date(scheduledDate);
    // Use lesson duration if provided, otherwise use course default, otherwise 180 minutes (3h)
    const actualDuration = duration || getDefaultLessonDuration(course);
    const endDate = new Date(date.getTime() + actualDuration * 60000);
    
    return {
      date: date.toLocaleDateString('pt-BR'),
      time: `${date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })} - ${endDate.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}`
    };
  };

  const getLessonStatusBadge = (lesson: Lesson) => {
    const isPast = isLessonPast(lesson.scheduledDate);
    
    if (isPast) {
      return { variant: 'secondary' as const, text: 'Realizada' };
    } else {
      return { variant: 'success' as const, text: 'Agendada' };
    }
  };

  // Loading state - only show full page loading for non-list modes
  if (loading || (isLoading && mode !== 'list')) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">
            {mode === 'edit' ? 'Carregando turma...' : 'Carregando turmas...'}
          </p>
        </div>
      </div>
    );
  }

  // Permission check
  if (!canManageClasses(user)) {
    return null;
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-destructive">Erro</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
            <Button onClick={() => navigateToList()} className="mt-4">
              Voltar para Lista
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render based on mode
  if (mode === 'new') {
    return (
      <AdminPageLayout
        title="Nova Turma"
        description="Criar uma nova turma para um curso"
        icon={CalendarPlus}
        backUrl="/admin/classes"
      >
        <FormCard title="Cadastrar Nova Turma" icon={Calendar}>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 xl:grid-cols-3">
              <ModernInput
                label="Nome da Turma"
                value={formData.name}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Ex: Academy Turma 01, Master Manhã..."
                icon={Calendar}
                required
                className="md:col-span-2 xl:col-span-1"
              />

              <ModernSelect
                label="Curso"
                value={formData.courseId}
                onValueChange={(value) => {
                  const newEndDate = calculateEndDate(formData.startDate, value);
                  setFormData({ ...formData, courseId: value, endDate: newEndDate });
                }}
                icon={BookOpen}
                options={courses.map(course => ({ value: course.id, label: course.name }))}
                placeholder="Selecione um curso"
              />

              <ModernInput
                label="Máximo de Alunos"
                type="number"
                min="1"
                value={formData.maxStudents}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, maxStudents: e.target.value })}
                placeholder="Ex: 20"
                icon={Users}
              />

              <ModernInput
                label="Data de Início"
                type="date"
                value={formData.startDate}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                  const newStartDate = e.target.value;
                  const newEndDate = calculateEndDate(newStartDate, formData.courseId);
                  setFormData({ ...formData, startDate: newStartDate, endDate: newEndDate });
                }}
                icon={Calendar}
              />

              <ModernInput
                label="Data de Término"
                type="date"
                value={formData.endDate}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, endDate: e.target.value })}
                icon={Calendar}
              />
            </div>

            <ModernTextarea
              label="Descrição"
              value={formData.description}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Descrição da turma..."
              rows={3}
              icon={FileText}
            />

            <FormActions
              onCancel={() => navigateToList()}
              isLoading={isSubmitting}
              submitLabel="Salvar Turma"
              cancelLabel="Cancelar"
              submitIcon={Save}
            />
          </form>
        </FormCard>
      </AdminPageLayout>
    );
  }

  if (mode === 'edit' && selectedClass) {
    return (
      <AdminPageLayout
        title="Editar Turma"
        description={`Edite as informações da turma ${selectedClass.name}`}
        icon={Calendar}
        backUrl="/admin/classes"
      >
        <FormCard 
          title="Informações da Turma" 
          icon={Calendar}
          rightContent={
            <AutoSaveIndicator
              isAutoSaving={autoSaveState.isAutoSaving}
              lastSaved={autoSaveState.lastSaved}
              hasUnsavedChanges={autoSaveState.hasUnsavedChanges}
              error={autoSaveState.error}
            />
          }
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 xl:grid-cols-3">
              <ModernInput
                label="Nome da Turma"
                value={formData.name}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Ex: Academy Turma 01, Master Manhã..."
                icon={Calendar}
                required
                className="md:col-span-2 xl:col-span-1"
              />

              <ModernSelect
                label="Curso"
                value={formData.courseId}
                onValueChange={(value) => {
                  const newEndDate = calculateEndDate(formData.startDate, value);
                  setFormData({ ...formData, courseId: value, endDate: newEndDate });
                }}
                icon={BookOpen}
                options={courses.map(course => ({ value: course.id, label: course.name }))}
                placeholder="Selecione um curso"
              />

              <ModernInput
                label="Máximo de Alunos"
                type="number"
                min="1"
                value={formData.maxStudents}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, maxStudents: e.target.value })}
                placeholder="Ex: 20"
                icon={Users}
              />

              <ModernInput
                label="Data de Início"
                type="date"
                value={formData.startDate}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                  const newStartDate = e.target.value;
                  const newEndDate = calculateEndDate(newStartDate, formData.courseId);
                  setFormData({ ...formData, startDate: newStartDate, endDate: newEndDate });
                }}
                icon={Calendar}
              />

              <ModernInput
                label="Data de Término"
                type="date"
                value={formData.endDate}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, endDate: e.target.value })}
                icon={Calendar}
              />
            </div>

            <ModernTextarea
              label="Descrição"
              value={formData.description}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Descrição da turma..."
              rows={3}
              icon={FileText}
            />

            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
              />
              <label htmlFor="isActive" className="text-sm font-medium">
                Turma ativa
              </label>
            </div>

            <div className="flex justify-end">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigateToList()}
                className="flex items-center"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar para Lista
              </Button>
            </div>
          </form>
        </FormCard>
      </AdminPageLayout>
    );
  }

  // Default list view
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="w-full max-w-[1600px] mx-auto">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-2xl p-8 text-white shadow-xl mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button onClick={() => router.push('/')} variant="outline" size="sm" className="bg-white/20 text-white border-white/30 hover:bg-white/30">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar
              </Button>
              <div>
                <h1 className="text-3xl font-bold mb-2 flex items-center">
                  <Calendar className="w-8 h-8 mr-3" />
                  Gerenciar Turmas
                </h1>
                <p className="text-white/90">
                  Administre turmas, horários e matrículas
                </p>
              </div>
            </div>
            <div className="hidden md:block">
              <Button 
                onClick={() => navigateToMode('new')}
                className="bg-white/20 text-white border-white/30 hover:bg-white/30 backdrop-blur-sm"
                size="lg"
              >
                <Plus className="w-5 h-5 mr-2" />
                Nova Turma
              </Button>
            </div>
          </div>
          {/* Mobile button */}
          <div className="md:hidden mt-4">
            <Button 
              onClick={() => navigateToMode('new')}
              className="bg-white/20 text-white border-white/30 hover:bg-white/30 backdrop-blur-sm w-full"
            >
              <Plus className="w-5 h-5 mr-2" />
              Nova Turma
            </Button>
          </div>
        </div>

        {/* Search Section */}
        <Card className="mb-6 shadow-lg border-0">
          <CardContent className="pt-6">
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Buscar turmas por nome, curso ou descrição..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  className="border-2 border-gray-200 focus:border-[#667eea] focus:ring-4 focus:ring-[#667eea]/20 transition-all duration-300"
                />
              </div>
              <Button 
                onClick={handleSearch}
                className="bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:from-[#5a6fd8] hover:to-[#6b4190] text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
              >
                <Search className="w-4 h-4 mr-2" />
                Buscar
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Classes Table */}
        <Card className="shadow-xl border-0">
          <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
            <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
              <Calendar className="w-6 h-6 mr-2 text-[#667eea]" />
              Turmas Cadastradas
            </CardTitle>
            <CardDescription className="text-lg">
              {classes.length} turma{classes.length !== 1 ? 's' : ''} encontrada{classes.length !== 1 ? 's' : ''}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading && mode === 'list' ? (
              <div className="text-center py-12">
                <Loader2 className="mx-auto animate-spin" size={32} />
                <p className="mt-4 text-muted-foreground">Carregando turmas...</p>
              </div>
            ) : classes.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-20 h-20 bg-gradient-to-br from-[#667eea] to-[#764ba2] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl">
                  <Calendar className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  Nenhuma turma encontrada
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  {searchTerm ? 'Tente uma busca diferente ou ajuste os filtros.' : 'Comece criando uma nova turma para organizar seus alunos.'}
                </p>
                {!searchTerm && (
                  <Button 
                    onClick={() => navigateToMode('new')} 
                    className="bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:from-[#5a6fd8] hover:to-[#6b4190] text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
                    size="lg"
                  >
                    <Plus className="w-5 h-5 mr-2" />
                    Criar Primeira Turma
                  </Button>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Turma</TableHead>
                      <TableHead>Curso</TableHead>
                      <TableHead>Período</TableHead>
                      <TableHead>Alunos</TableHead>
                      <TableHead>Aulas</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {classes.map((classItem) => (
                      <Fragment key={classItem.id}>
                        <TableRow>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleClassExpansion(classItem.id)}
                                className="p-1 h-auto"
                              >
                                {expandedClasses.has(classItem.id) ? (
                                  <ChevronDown className="w-4 h-4" />
                                ) : (
                                  <ChevronRight className="w-4 h-4" />
                                )}
                              </Button>
                              <div>
                                <div className="font-medium">{classItem.name}</div>
                                {classItem.description && (
                                  <div className="text-sm text-muted-foreground">
                                    {classItem.description}
                                  </div>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <BookOpen className="w-4 h-4 mr-2 text-[#667eea]" />
                              {classItem.course.name}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div>Início: {formatDateForDisplay(classItem.startDate)}</div>
                              {classItem.endDate && (
                                <div className="text-muted-foreground">
                                  Fim: {formatDateForDisplay(classItem.endDate)}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Users className="w-4 h-4 mr-1" />
                              {classItem._count.enrollments}
                              {classItem.maxStudents && ` / ${classItem.maxStudents}`}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Clock className="w-4 h-4 mr-1" />
                              {classItem._count.lessons}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant={classItem.isActive ? 'success' : 'secondary'}>
                              {classItem.isActive ? 'Ativa' : 'Inativa'}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button
                                onClick={() => navigateToMode('edit', classItem.id)}
                                variant="outline"
                                size="sm"
                                className="border-[#667eea] text-[#667eea] hover:bg-[#667eea] hover:text-white transition-all duration-300"
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button
                                onClick={() => handleDelete(classItem.id, classItem.name)}
                                variant="outline"
                                size="sm"
                                className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white transition-all duration-300"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                        
                        {/* Expanded details row - Two columns layout */}
                        {expandedClasses.has(classItem.id) && (
                          <TableRow>
                            <TableCell colSpan={7} className="bg-gray-50 p-0">
                              <div className="p-6">
                                {/* Two columns grid */}
                                <div className="grid gap-6 lg:grid-cols-2">
                                  
                                  {/* Left Column - Students */}
                                  <div className="bg-white rounded-lg p-4 shadow-sm border">
                                    <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                                      <Users className="w-5 h-5 mr-2 text-[#667eea]" />
                                      Alunos Matriculados ({classItem._count.enrollments})
                                    </h4>
                                    
                                    {loadingStudents.has(classItem.id) ? (
                                      <div className="flex items-center justify-center py-8">
                                        <Loader2 className="w-5 h-5 animate-spin mr-2 text-[#667eea]" />
                                        <span className="text-gray-600">Carregando alunos...</span>
                                      </div>
                                    ) : classStudents[classItem.id]?.length > 0 ? (
                                      <div className="space-y-3 max-h-80 overflow-y-auto">
                                        {classStudents[classItem.id].map((student) => (
                                          <div key={student.id} className="bg-gray-50 p-3 rounded-lg flex items-center justify-between hover:bg-gray-100 transition-colors">
                                            <div className="flex-1">
                                              <div className="font-medium text-gray-900">{student.name}</div>
                                              <div className="text-sm text-gray-600 flex items-center space-x-4 mt-1">
                                                {student.email && (
                                                  <span className="flex items-center">
                                                    <Mail className="w-3 h-3 mr-1" />
                                                    {student.email}
                                                  </span>
                                                )}
                                                {student.phone && (
                                                  <span className="flex items-center">
                                                    <Phone className="w-3 h-3 mr-1" />
                                                    {student.phone}
                                                  </span>
                                                )}
                                              </div>
                                            </div>
                                            <Badge variant={student.status === 'active' ? 'success' : 'secondary'} className="ml-3">
                                              {student.status === 'active' ? 'Ativo' : 'Inativo'}
                                            </Badge>
                                          </div>
                                        ))}
                                      </div>
                                    ) : (
                                      <div className="text-center py-8 text-gray-500">
                                        <Users className="w-12 h-12 mx-auto mb-3 opacity-50" />
                                        <p className="font-medium">Nenhum aluno matriculado</p>
                                        <p className="text-sm">Esta turma ainda não possui alunos.</p>
                                      </div>
                                    )}
                                  </div>

                                  {/* Right Column - Lessons */}
                                  <div className="bg-white rounded-lg p-4 shadow-sm border">
                                    <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                                      <Clock className="w-5 h-5 mr-2 text-[#667eea]" />
                                      Aulas ({classItem._count.lessons})
                                    </h4>
                                    
                                    {loadingLessons.has(classItem.id) ? (
                                      <div className="flex items-center justify-center py-8">
                                        <Loader2 className="w-5 h-5 animate-spin mr-2 text-[#667eea]" />
                                        <span className="text-gray-600">Carregando aulas...</span>
                                      </div>
                                    ) : classLessons[classItem.id]?.length > 0 ? (
                                      <div className="space-y-3 max-h-80 overflow-y-auto">
                                        {classLessons[classItem.id].map((lesson) => {
                                          const dateTime = formatLessonDateTime(lesson.scheduledDate, lesson.duration, classItem.course);
                                          const statusBadge = getLessonStatusBadge(lesson);
                                          const isPast = isLessonPast(lesson.scheduledDate);
                                          
                                          const isCurrentLesson = (() => {
                                            const now = new Date();
                                            const lessonDate = new Date(lesson.scheduledDate);
                                            
                                            // Check if this is the next upcoming lesson or current lesson
                                            const allLessons = classLessons[classItem.id] || [];
                                            const upcomingLessons = allLessons.filter(l => new Date(l.scheduledDate) >= now);
                                            
                                            if (upcomingLessons.length > 0) {
                                              return lesson.id === upcomingLessons[0].id;
                                            } else {
                                              // If no upcoming lessons, highlight the last lesson
                                              return lesson.id === allLessons[allLessons.length - 1]?.id;
                                            }
                                          })();

                                          return (
                                            <div 
                                              key={lesson.id} 
                                              data-lesson-id={lesson.id}
                                              className={`bg-gray-50 p-3 rounded-lg hover:bg-gray-100 transition-colors ${
                                                isCurrentLesson ? 'ring-2 ring-[#667eea] bg-blue-50' : ''
                                              }`}
                                            >
                                              <div className="flex items-start justify-between mb-2">
                                                <div className="flex-1">
                                                  <div className={`font-medium ${isCurrentLesson ? 'text-[#667eea]' : 'text-gray-900'}`}>
                                                    {lesson.title}
                                                    {isCurrentLesson && <span className="ml-2 text-xs font-normal text-[#667eea]">● Atual</span>}
                                                  </div>
                                                  {lesson.description && (
                                                    <div className="text-sm text-gray-600 mt-1">{lesson.description}</div>
                                                  )}
                                                </div>
                                                <Badge variant={statusBadge.variant} className="ml-2">
                                                  {statusBadge.text}
                                                </Badge>
                                              </div>
                                              
                                              <div className="text-xs text-gray-500 space-y-1">
                                                <div className="flex items-center">
                                                  <Calendar className="w-3 h-3 mr-1" />
                                                  {dateTime.date} • {dateTime.time}
                                                </div>
                                                
                                                {lesson.location && (
                                                  <div className="flex items-center">
                                                    <span className="w-3 h-3 mr-1 text-center">📍</span>
                                                    {lesson.location}
                                                  </div>
                                                )}
                                                
                                                {isPast && (
                                                  <div className="flex items-center space-x-4 mt-2 pt-2 border-t border-gray-200">
                                                    <span className="flex items-center text-green-600">
                                                      <span className="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                                                      {lesson._count.attendance} presentes
                                                    </span>
                                                    {lesson.attendanceStats && (
                                                      <>
                                                        <span className="flex items-center text-red-600">
                                                          <span className="w-2 h-2 bg-red-500 rounded-full mr-1"></span>
                                                          {lesson.attendanceStats.absent} ausentes
                                                        </span>
                                                        {lesson.attendanceStats.makeup > 0 && (
                                                          <span className="flex items-center text-orange-600">
                                                            <span className="w-2 h-2 bg-orange-500 rounded-full mr-1"></span>
                                                            {lesson.attendanceStats.makeup} reposição
                                                          </span>
                                                        )}
                                                      </>
                                                    )}
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                          );
                                        })}
                                      </div>
                                    ) : (
                                      <div className="text-center py-8 text-gray-500">
                                        <Clock className="w-12 h-12 mx-auto mb-3 opacity-50" />
                                        <p className="font-medium">Nenhuma aula agendada</p>
                                        <p className="text-sm">Esta turma ainda não possui aulas.</p>
                                      </div>
                                    )}
                                  </div>
                                  
                                </div>
                              </div>
                            </TableCell>
                          </TableRow>
                        )}
                      </Fragment>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      <ConfirmationDialog />
    </div>
  );
}

export default function ClassesPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">Carregando...</p>
        </div>
      </div>
    }>
      <ClassesPageContent />
    </Suspense>
  );
}