import { NextRequest, NextResponse } from 'next/server';
import { StudentLessonsService } from '@/lib/services/student-lessons.service';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; classId: string }> }
) {
  try {
    const { id: studentId, classId } = await params;

    if (!studentId || !classId) {
      return NextResponse.json(
        { error: 'ID do estudante e ID da turma são obrigatórios' },
        { status: 400 }
      );
    }

    const result = await StudentLessonsService.getStudentLessons(studentId, classId);

    if (!result) {
      return NextResponse.json(
        { error: 'Estudante não está matriculado nesta turma' },
        { status: 403 }
      );
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error('Erro ao buscar aulas do estudante:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}