import { NextRequest, NextResponse } from 'next/server';
import { CourseLessonTemplatesService } from '@/lib/services/course-lesson-templates.service';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: courseId } = await params;

    const courseWithTemplates = await CourseLessonTemplatesService.getCourseWithTemplates(courseId);

    if (!courseWithTemplates) {
      return NextResponse.json(
        { error: 'Curso não encontrado' },
        { status: 404 }
      );
    }

    return NextResponse.json({ data: courseWithTemplates });

  } catch (error) {
    console.error('Erro ao buscar templates do curso:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: courseId } = await params;
    const body = await request.json();
    const { templates } = body;

    if (!Array.isArray(templates)) {
      return NextResponse.json(
        { error: 'Templates deve ser um array' },
        { status: 400 }
      );
    }

    const result = await CourseLessonTemplatesService.updateCourseTemplates(courseId, templates);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Templates atualizados com sucesso' 
    });

  } catch (error) {
    console.error('Erro ao atualizar templates do curso:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: courseId } = await params;
    const body = await request.json();
    const { action, numberOfLessons } = body;

    if (action === 'generate-default') {
      if (!numberOfLessons || numberOfLessons <= 0) {
        return NextResponse.json(
          { error: 'Número de aulas deve ser maior que zero' },
          { status: 400 }
        );
      }

      const result = await CourseLessonTemplatesService.generateDefaultTemplates(courseId, numberOfLessons);

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 400 }
        );
      }

      return NextResponse.json({ 
        success: true, 
        message: 'Templates padrão gerados com sucesso' 
      });

    } else if (action === 'apply-to-lessons') {
      const result = await CourseLessonTemplatesService.applyTemplatesToLessons(courseId);

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 400 }
        );
      }

      return NextResponse.json({ 
        success: true, 
        message: `Templates aplicados a ${result.updated} aulas existentes`,
        updated: result.updated
      });

    } else {
      return NextResponse.json(
        { error: 'Ação não reconhecida' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Erro ao processar ação de templates:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}