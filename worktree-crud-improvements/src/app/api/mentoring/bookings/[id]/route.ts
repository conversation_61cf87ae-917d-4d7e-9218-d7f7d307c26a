import { NextRequest, NextResponse } from 'next/server';
import { mentoringService } from '@/lib/services/mentoring.service';
import { verifyToken } from '@/lib/auth';

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await verifyToken(token);
    
    if (!user) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    
    // Handle cancellation
    if (body.action === 'cancel') {
      const result = await mentoringService.cancelMentoringBooking(id, body.reason);
      
      if (!result.success) {
        return NextResponse.json({ error: result.error }, { status: 400 });
      }

      return NextResponse.json({ 
        success: true, 
        data: result.data 
      });
    }

    // Handle completion
    if (body.action === 'complete') {
      const result = await mentoringService.completeMentoringBooking(id);
      
      if (!result.success) {
        return NextResponse.json({ error: result.error }, { status: 400 });
      }

      return NextResponse.json({ 
        success: true, 
        data: result.data 
      });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });

  } catch (error) {
    console.error('Error in mentoring booking PUT:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}