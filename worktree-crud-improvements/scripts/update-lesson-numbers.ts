import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function updateLessonNumbers() {
  try {
    console.log('Starting lesson number update...');

    // Get all classes with their lessons
    const classes = await prisma.class.findMany({
      include: {
        lessons: {
          orderBy: {
            scheduledDate: 'asc'
          }
        }
      }
    });

    let totalUpdated = 0;

    for (const classData of classes) {
      console.log(`Processing class: ${classData.name} (${classData.lessons.length} lessons)`);
      
      for (let i = 0; i < classData.lessons.length; i++) {
        const lesson = classData.lessons[i];
        const lessonNumber = i + 1;
        
        // Update lesson with lesson number if it doesn't have one
        if (lesson.lessonNumber !== lessonNumber) {
          await prisma.lesson.update({
            where: { id: lesson.id },
            data: { 
              lessonNumber: lessonNumber,
              // Also update title to ensure consistency
              title: `Aula ${lessonNumber}`
            }
          });
          
          console.log(`  Updated lesson ${lesson.id}: "${lesson.title}" -> "Aula ${lessonNumber}" (number: ${lessonNumber})`);
          totalUpdated++;
        }
      }
    }

    console.log(`\n✅ Update completed! ${totalUpdated} lessons updated.`);
  } catch (error) {
    console.error('❌ Error updating lesson numbers:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateLessonNumbers();