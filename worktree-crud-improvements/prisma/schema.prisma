// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// Enums
enum DayOfWeek {
  monday
  tuesday
  wednesday
  thursday
  friday
  saturday
  sunday
}

enum PresenceStatus {
  present
  absent
}

enum EnrollmentType {
  regular
  guest
  restart
}

enum EnrollmentStatus {
  active
  inactive
  completed
  transferred
}


// User authentication and profiles (passwordless with magic links)
model User {
  id            String   @id @default(cuid())
  email         String   @unique
  emailVerified <PERSON>ole<PERSON>  @default(false) @map("email_verified")
  lastLoginAt   DateTime? @map("last_login_at")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  profile       UserProfile?
  magicLinks    MagicLink[]
  sessions      Session[]
  auditLogs     AuditLog[]
  securityEvents SecurityEvent[]

  @@map("users")
}

// Magic links for passwordless authentication
model MagicLink {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  token     String   @unique
  email     String
  expiresAt DateTime @map("expires_at")
  usedAt    DateTime? @map("used_at")
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("magic_links")
}

// User sessions
model Session {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  token     String   @unique
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model UserProfile {
  id        String   @id @default(cuid())
  userId    String   @unique @map("user_id")
  fullName  String?  @map("full_name")
  role      String   @default("user")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_profiles")
}

// Students and academic data
model Student {
  id                String   @id @default(cuid())
  name              String
  email             String?
  phone             String?
  birthDate         DateTime? @map("birth_date")
  registrationDate  DateTime @default(now()) @map("registration_date")
  status            String   @default("active")
  notes             String?

  // Facial recognition fields
  faceDescriptor    String?  @map("face_descriptor") // JSON string of face descriptor array
  photoUrl          String?  @map("photo_url") // URL to reference photo
  faceDataUpdatedAt DateTime? @map("face_data_updated_at") // When face data was last updated

  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  // Relations
  attendance        Attendance[]
  enrollments       Enrollment[]
  mentoringBookings MentoringBooking[]

  @@map("students")
}

// Teachers
model Teacher {
  id               String   @id @default(cuid())
  name             String
  email            String?
  phone            String?
  specialization   String?  // Area of expertise
  status           String   @default("active") // active, inactive
  notes            String?
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // Relations
  classes          Class[]
  lessons          Lesson[]
  mentoringSlots   MentoringSlot[]
  mentoringBookings MentoringBooking[]

  @@map("teachers")
}

model Course {
  id             String   @id @default(cuid())
  name           String
  description    String?
  duration       Int?     // Duration in hours
  numberOfLessons Int?    @map("number_of_lessons") // Number of lessons in the course
  lessonDuration Int?     @default(180) @map("lesson_duration") // Duration of each lesson in minutes (default 3h = 180min)
  price          Float?
  allowsMakeup   Boolean @default(false) @map("allows_makeup") // Flag for makeup classes
  isActive       Boolean  @default(true) @map("is_active")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  classes     Class[]
  enrollments Enrollment[]
  lessonTemplates CourseLessonTemplate[]
  mentoringBookings MentoringBooking[]

  @@map("courses")
}

// Course lesson templates - defines custom titles for lessons
model CourseLessonTemplate {
  id           String @id @default(cuid())
  courseId     String @map("course_id")
  lessonNumber Int    @map("lesson_number") // 1, 2, 3, etc.
  title        String // Custom title for this lesson
  description  String?
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  course Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  @@unique([courseId, lessonNumber])
  @@map("course_lesson_templates")
}

// Mentoring system models
model MentoringSlot {
  id                String   @id @default(cuid())
  teacherId         String   @map("teacher_id")
  dayOfWeek         DayOfWeek @map("day_of_week") // Using existing enum
  startTime         String   @map("start_time") // Format: "HH:MM"
  endTime           String   @map("end_time")   // Format: "HH:MM"
  duration          Int      @default(60) // Duration in minutes
  isActive          Boolean  @default(true) @map("is_active")
  maxBookingsPerWeek Int?    @default(5) @map("max_bookings_per_week") // Optional limit
  
  // Google Calendar integration
  googleCalendarId  String?  @map("google_calendar_id") // Calendar ID for this slot
  
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  // Relations
  teacher           Teacher @relation(fields: [teacherId], references: [id], onDelete: Cascade)
  bookings          MentoringBooking[]

  @@map("mentoring_slots")
}

model MentoringBooking {
  id                String   @id @default(cuid())
  studentId         String   @map("student_id")
  teacherId         String   @map("teacher_id")
  courseId          String?  @map("course_id") // Optional: which course this mentoring is for
  slotId            String   @map("slot_id")
  
  // Scheduling details
  scheduledDate     DateTime @map("scheduled_date")
  startTime         String   @map("start_time") // Format: "HH:MM"
  endTime           String   @map("end_time")   // Format: "HH:MM"
  duration          Int      @default(60) // Duration in minutes
  
  // Status and management
  status            String   @default("scheduled") // scheduled, completed, cancelled, rescheduled
  notes             String?  // Notes from student or teacher
  feedback          String?  // Post-mentoring feedback
  
  // Google Calendar integration
  googleEventId     String?  @map("google_event_id") // Google Calendar event ID
  
  // Reminder management
  reminderSent      Boolean  @default(false) @map("reminder_sent")
  reminderTemplate  String?  @map("reminder_template") // Template name used for reminder
  reminderSentAt    DateTime? @map("reminder_sent_at")
  
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")
  cancelledAt       DateTime? @map("cancelled_at")
  rescheduledAt     DateTime? @map("rescheduled_at")

  // Relations
  student           Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)
  teacher           Teacher  @relation(fields: [teacherId], references: [id], onDelete: Cascade)
  course            Course?  @relation(fields: [courseId], references: [id], onDelete: SetNull)
  slot              MentoringSlot @relation(fields: [slotId], references: [id], onDelete: Cascade)

  @@map("mentoring_bookings")
}

model Class {
  id          String    @id @default(cuid())
  courseId    String    @map("course_id")
  teacherId   String?   @map("teacher_id") // Optional teacher assignment
  name        String
  description String?
  startDate   DateTime  @map("start_date")
  endDate     DateTime? @map("end_date")
  classTime   String?   @default("19:00") @map("class_time") // Default class time
  schedule    String?   // JSON string for schedule
  maxStudents Int?      @map("max_students")
  isActive    Boolean   @default(true) @map("is_active")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // Relations
  course      Course     @relation(fields: [courseId], references: [id], onDelete: Cascade)
  teacher     Teacher?   @relation(fields: [teacherId], references: [id], onDelete: SetNull)
  lessons     Lesson[]
  enrollments Enrollment[]

  @@map("classes")
}

model Lesson {
  id                String   @id @default(cuid())
  classId           String   @map("class_id")
  teacherId         String?  @map("teacher_id") // Optional teacher override for specific lesson
  title             String
  description       String?
  lessonNumber      Int?     @map("lesson_number") // Sequential lesson number within the class
  scheduledDate     DateTime @map("scheduled_date")
  duration          Int?     // Duration in minutes
  location          String?
  isCompleted       Boolean  @default(false) @map("is_completed")
  notes             String?
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  // Relations
  class             Class        @relation(fields: [classId], references: [id], onDelete: Cascade)
  teacher           Teacher?     @relation(fields: [teacherId], references: [id], onDelete: SetNull)
  attendance        Attendance[]
  originalAttendance Attendance[] @relation("OriginalLesson")

  @@map("lessons")
}

model Enrollment {
  id                String           @id @default(cuid())
  studentId         String           @map("student_id")
  courseId          String           @map("course_id")
  classId           String?          @map("class_id")
  enrolledAt        DateTime         @default(now()) @map("enrolled_at")
  status            EnrollmentStatus @default(active)
  type              EnrollmentType   @default(regular)
  absenceCount      Int              @default(0) @map("absence_count")
  inactivatedAt     DateTime?        @map("inactivated_at")
  reactivatedAt     DateTime?        @map("reactivated_at")
  transferredFromId String?          @map("transferred_from_id") // ID da matrícula anterior em caso de transferência
  notes             String?
  createdAt         DateTime         @default(now()) @map("created_at")
  updatedAt         DateTime         @updatedAt @map("updated_at")

  // Relations
  student           Student      @relation(fields: [studentId], references: [id], onDelete: Cascade)
  course            Course       @relation(fields: [courseId], references: [id], onDelete: Cascade)
  class             Class?       @relation(fields: [classId], references: [id], onDelete: SetNull)
  transferredFrom   Enrollment?  @relation("EnrollmentTransfer", fields: [transferredFromId], references: [id], onDelete: SetNull)
  transferredTo     Enrollment[] @relation("EnrollmentTransfer")

  @@unique([studentId, courseId, classId])
  @@map("enrollments")
}

model Attendance {
  id                          String        @id @default(cuid())
  studentId                   String        @map("student_id")
  lessonId                    String        @map("lesson_id")
  originalLessonId            String?       @map("original_lesson_id")
  status                      PresenceStatus
  markedAt                    DateTime?     @map("marked_at")
  markedByFacialRecognition   Boolean       @default(false) @map("marked_by_facial_recognition")
  notes                       String?
  createdAt                   DateTime      @default(now()) @map("created_at")
  updatedAt                   DateTime      @updatedAt @map("updated_at")

  // Relations
  student        Student @relation(fields: [studentId], references: [id], onDelete: Cascade)
  lesson         Lesson  @relation(fields: [lessonId], references: [id], onDelete: Cascade)
  originalLesson Lesson? @relation("OriginalLesson", fields: [originalLessonId], references: [id], onDelete: SetNull)

  @@unique([studentId, lessonId])
  @@map("attendance")
}

// Messaging and reminders
model ReminderTemplate {
  id          String      @id @default(cuid())
  name        String      // Template name (e.g., "Lembrete Aula Amanhã", "Agendamento Mentoria")
  category    String?     // Optional category for organization (e.g., "aula", "mentoria", "reposicao")
  template    String      // Message template with placeholders like {{nome_do_aluno}}
  description String?     // Optional description of the template
  isActive    Boolean     @default(true) @map("is_active")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  @@map("reminder_templates")
}

model SmsReminder {
  id             String      @id @default(cuid())
  recipientName  String      @map("recipient_name")
  recipientPhone String      @map("recipient_phone")
  messageText    String      @map("message_text")
  messageType    String @map("message_type")
  sentAt         DateTime?   @map("sent_at")
  deliveryStatus String?     @map("delivery_status")
  errorMessage   String?     @map("error_message")
  createdAt      DateTime    @default(now()) @map("created_at")

  @@map("sms_reminders")
}

model WhatsAppMessage {
  id             String      @id @default(cuid())
  recipientPhone String      @map("recipient_phone")
  messageText    String      @map("message_text")
  messageId      String?     @map("message_id") // WhatsApp message ID
  messageType    String? @map("message_type") // Optional: type of reminder
  sentAt         DateTime?   @map("sent_at")
  deliveryStatus String      @default("pending") @map("delivery_status") // pending, sent, delivered, failed
  errorMessage   String?     @map("error_message")
  createdAt      DateTime    @default(now()) @map("created_at")
  updatedAt      DateTime    @updatedAt @map("updated_at")

  @@map("whatsapp_messages")
}

model WhatsAppSettings {
  id                    String   @id @default(cuid())
  enabled               Boolean  @default(false)
  sessionData           String?  @map("session_data") // Encrypted session data
  qrCode                String?  @map("qr_code") // Current QR code for authentication
  isAuthenticated       Boolean  @default(false) @map("is_authenticated")
  phoneNumber           String?  @map("phone_number") // Connected WhatsApp number
  lastConnectionCheck   DateTime? @map("last_connection_check")
  rateLimitSeconds      Int      @default(30) @map("rate_limit_seconds")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  @@map("whatsapp_settings")
}

model MessageQueue {
  id             String      @id @default(cuid())
  recipientPhone String      @map("recipient_phone")
  messageText    String      @map("message_text")
  messageType    String? @map("message_type")
  priority       Int         @default(3) // 1 = highest, 5 = lowest
  scheduledFor   DateTime    @map("scheduled_for")
  attempts       Int         @default(0)
  maxAttempts    Int         @default(3) @map("max_attempts")
  status         String      @default("pending") // pending, processing, sent, failed, cancelled
  sentAt         DateTime?   @map("sent_at")
  lastAttemptAt  DateTime?   @map("last_attempt_at")
  errorMessage   String?     @map("error_message")
  metadata       String?     // JSON string for additional data
  createdAt      DateTime    @default(now()) @map("created_at")
  updatedAt      DateTime    @updatedAt @map("updated_at")

  @@index([status, scheduledFor])
  @@index([priority, scheduledFor])
  @@map("message_queue")
}

model WhatsAppLog {
  id           String   @id @default(cuid())
  level        String   // debug, info, warn, error, critical
  eventType    String   @map("event_type") // connection_established, message_sent, etc.
  message      String
  metadata     String?  // JSON string for additional data
  errorMessage String?  @map("error_message")
  errorStack   String?  @map("error_stack")
  userId       String?  @map("user_id")
  phoneNumber  String?  @map("phone_number")
  messageId    String?  @map("message_id")
  timestamp    DateTime @default(now())

  @@index([level, timestamp])
  @@index([eventType, timestamp])
  @@index([phoneNumber, timestamp])
  @@map("whatsapp_logs")
}

// Audit and security
model AuditLog {
  id          String   @id @default(cuid())
  userId      String?  @map("user_id")
  action      String
  tableName   String   @map("table_name")
  recordId    String?  @map("record_id")
  oldValues   String?  @map("old_values") // JSON string
  newValues   String?  @map("new_values") // JSON string
  timestamp   DateTime @default(now())

  // Relations
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("audit_logs")
}

model SecurityEvent {
  id          String   @id @default(cuid())
  userId      String?  @map("user_id")
  eventType   String   @map("event_type")
  severity    String
  description String
  metadata    String?  // JSON string
  ipAddress   String?  @map("ip_address")
  userAgent   String?  @map("user_agent")
  timestamp   DateTime @default(now())

  // Relations
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("security_events")
}
