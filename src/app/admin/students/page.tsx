'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState, useCallback, Suspense } from 'react';
import { toast } from 'sonner';
import { canManageStudents } from '@/lib/roles';
import { studentsService } from '@/lib/services/students.service';
import { useAutoSave } from '@/hooks/useAutoSave';
import { AutoSaveIndicator } from '@/components/ui/auto-save-indicator';

// Components
import {
  AdminPageLayout,
  FormCard,
  ModernInput,
  ModernButton,
  ModernTextarea,
  ModernSelect,
  Button,
  Badge
} from '@/components/ui/modern';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { FaceRegistration } from '@/components/FaceRegistration';
import { StudentEnrollmentsView } from '@/components/StudentEnrollmentsView';

// Icons
import { 
  Loader2, Users, ArrowLeft, Plus, Search, Edit, Trash2, Mail, Phone, 
  Camera, UserPlus, Calendar, FileText, Activity, User
} from 'lucide-react';

interface Student {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  birthDate?: string;
  notes?: string;
  status: string;
  registrationDate: string;
  faceDescriptor?: string;
  photoUrl?: string;
  faceDataUpdatedAt?: string;
  _count?: {
    attendance: number;
    enrollments: number;
  };
  enrollments?: Array<{
    course: {
      name: string;
    };
    class?: {
      name: string;
    };
  }>;
}

interface StudentFormData {
  name: string;
  email: string;
  phone: string;
  birthDate: string;
  notes: string;
  status: string;
}

function StudentsPageContent() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get mode and ID from URL parameters
  const mode = searchParams.get('mode') || 'list'; // list, new, edit
  const studentId = searchParams.get('id');
  
  // State
  const [students, setStudents] = useState<Student[]>([]);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [faceRegistrationOpen, setFaceRegistrationOpen] = useState(false);
  const { showConfirmation, ConfirmationDialog } = useConfirmationDialog();
  
  const [formData, setFormData] = useState<StudentFormData>({
    name: '',
    email: '',
    phone: '',
    birthDate: '',
    notes: '',
    status: 'active'
  });

  // Navigation helpers
  const navigateToMode = useCallback((newMode: string, id?: string) => {
    const params = new URLSearchParams();
    params.set('mode', newMode);
    if (id) params.set('id', id);
    router.push(`/admin/students?${params.toString()}`);
  }, [router]);

  const navigateToList = useCallback(() => {
    router.push('/admin/students');
  }, [router]);

  // Data fetching
  const fetchStudents = useCallback(async (search?: string) => {
    try {
      // Only show loading for table content, not full page
      if (mode === 'list') {
        setIsLoading(true);
      }
      setError(null);
      const url = search
        ? `/api/students?search=${encodeURIComponent(search)}`
        : '/api/students';

      const token = localStorage.getItem('auth_token');
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Falha ao carregar alunos');
      }

      const result = await response.json();
      setStudents(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      // Only hide loading for table content
      if (mode === 'list') {
        setIsLoading(false);
      }
    }
  }, [mode]);

  const fetchStudent = useCallback(async (id: string) => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/students/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Falha ao carregar aluno');
      }

      const result = await response.json();
      const studentData = result.data;
      setSelectedStudent(studentData);

      // Populate form for editing
      setFormData({
        name: studentData.name || '',
        email: studentData.email || '',
        phone: studentData.phone || '',
        birthDate: studentData.birthDate ? studentData.birthDate.split('T')[0] : '',
        notes: studentData.notes || '',
        status: studentData.status || 'active'
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Effects
  useEffect(() => {
    if (!loading && !canManageStudents(user)) {
      router.push('/');
      return;
    }

    if (canManageStudents(user)) {
      if (mode === 'edit' && studentId) {
        fetchStudent(studentId);
      } else if (mode === 'list') {
        fetchStudents('');
      } else if (mode === 'new') {
        setIsLoading(false);
        // Reset form for new student
        setFormData({
          name: '',
          email: '',
          phone: '',
          birthDate: '',
          notes: '',
          status: 'active'
        });
      }
    }
  }, [user, loading, router, mode, studentId, fetchStudent, fetchStudents]);

  // Auto-save functionality for edit mode
  const autoSaveStudent = useCallback(async (data: StudentFormData) => {
    if (mode !== 'edit' || !studentId || !data.name.trim()) {
      return false;
    }

    try {
      const token = localStorage.getItem('auth_token');
      if (!token) return false;

      const response = await fetch(`/api/students/${studentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          name: data.name.trim(),
          email: data.email.trim() || null,
          phone: data.phone.trim() || null,
          birthDate: data.birthDate || null,
          notes: data.notes.trim() || null,
          status: data.status
        }),
      });

      return response.ok;
    } catch (err) {
      console.error('Auto-save error:', err);
      return false;
    }
  }, [mode, studentId]);

  const autoSaveState = useAutoSave({
    data: formData,
    onSave: autoSaveStudent,
    config: {
      debounceMs: 2000,
      enableToast: false
    },
    isValid: (data) => !!data.name.trim(),
    enabled: mode === 'edit'
  });

  // Form handlers
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Nome é obrigatório');
      return;
    }

    try {
      setIsSubmitting(true);
      const token = localStorage.getItem('auth_token');

      if (!token) {
        toast.error('Sessão expirada. Faça login novamente.');
        router.push('/login');
        return;
      }

      if (mode === 'new') {
        const response = await studentsService.createStudent({
          name: formData.name.trim(),
          email: formData.email.trim() || undefined,
          phone: formData.phone.trim() || undefined
        });

        if (!response.success) {
          throw new Error(response.error || 'Falha ao criar aluno');
        }

        toast.success('Aluno criado com sucesso!');
      } else if (mode === 'edit' && studentId) {
        const response = await fetch(`/api/students/${studentId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            name: formData.name.trim(),
            email: formData.email.trim() || null,
            phone: formData.phone.trim() || null,
            birthDate: formData.birthDate || null,
            notes: formData.notes.trim() || null,
            status: formData.status
          }),
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Falha ao atualizar aluno');
        }

        toast.success('Aluno atualizado com sucesso!');
      }

      navigateToList();
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Erro ao salvar aluno');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSearch = useCallback(() => {
    fetchStudents(searchTerm);
  }, [fetchStudents, searchTerm]);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (mode === 'list') {
        fetchStudents(searchTerm);
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchTerm, mode, fetchStudents]);

  const handleDelete = (studentId: string, studentName: string) => {
    showConfirmation({
      title: 'Excluir Aluno',
      description: `Tem certeza que deseja excluir o aluno "${studentName}"? Esta ação não pode ser desfeita.`,
      confirmText: 'Excluir',
      cancelText: 'Cancelar',
      variant: 'destructive',
      icon: 'delete',
      onConfirm: async () => {
        try {
          const token = localStorage.getItem('auth_token');

          if (!token) {
            toast.error('Sessão expirada. Faça login novamente.');
            router.push('/login');
            return;
          }

          const response = await fetch(`/api/students/${studentId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Failed to delete student');
          }

          toast.success('Aluno excluído com sucesso!');
          fetchStudents(searchTerm);
        } catch (err) {
          toast.error(err instanceof Error ? err.message : 'Erro ao excluir aluno');
        }
      }
    });
  };

  const handleFaceRegistration = (student: Student) => {
    setSelectedStudent(student);
    setFaceRegistrationOpen(true);
  };

  const handleFaceRegistrationComplete = (studentId: string) => {
    if (mode === 'edit') {
      fetchStudent(studentId);
    } else {
      fetchStudents();
    }
    setFaceRegistrationOpen(false);
    toast.success('Dados faciais atualizados com sucesso!');
  };

  const handleRemoveFaceData = (student: Student) => {
    showConfirmation({
      title: 'Remover Dados Faciais',
      description: `Tem certeza que deseja remover os dados faciais de "${student.name}"? Esta ação não pode ser desfeita.`,
      confirmText: 'Remover',
      cancelText: 'Cancelar',
      variant: 'destructive',
      icon: 'delete',
      onConfirm: async () => {
        try {
          const token = localStorage.getItem('auth_token');

          if (!token) {
            toast.error('Sessão expirada. Faça login novamente.');
            router.push('/login');
            return;
          }

          const response = await fetch(`/api/students/${student.id}/face-data`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Falha ao remover dados faciais');
          }

          toast.success('Dados faciais removidos com sucesso!');
          
          // Refresh data
          if (mode === 'edit') {
            fetchStudent(student.id);
          } else {
            fetchStudents(searchTerm);
          }
        } catch (err) {
          toast.error(err instanceof Error ? err.message : 'Erro ao remover dados faciais');
        }
      }
    });
  };

  // Utility functions
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'secondary';
      case 'suspended':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Ativo';
      case 'inactive':
        return 'Inativo';
      case 'suspended':
        return 'Suspenso';
      default:
        return status;
    }
  };

  // Loading state - only show full page loading for non-list modes
  if (loading || (isLoading && mode !== 'list')) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">
            {mode === 'edit' ? 'Carregando aluno...' : 'Carregando alunos...'}
          </p>
        </div>
      </div>
    );
  }

  // Permission check
  if (!canManageStudents(user)) {
    return null;
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-destructive">Erro</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
            <Button onClick={() => navigateToList()} className="mt-4">
              Voltar para Lista
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render based on mode
  if (mode === 'new') {
    return (
      <AdminPageLayout
        title="Novo Aluno"
        description="Cadastrar um novo aluno no sistema"
        icon={UserPlus}
        backUrl="/admin/students"
      >
        <FormCard title="Cadastrar Novo Aluno" icon={Users}>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 xl:grid-cols-3">
              <ModernInput
                label="Nome"
                value={formData.name}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Nome completo do aluno"
                icon={User}
                required
                className="md:col-span-2 xl:col-span-1"
              />

              <ModernInput
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, email: e.target.value })}
                placeholder="<EMAIL>"
                icon={Mail}
              />

              <ModernInput
                label="Telefone"
                value={formData.phone}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, phone: e.target.value })}
                placeholder="(11) 99999-9999"
                icon={Phone}
              />

              <ModernInput
                label="Data de Nascimento"
                type="date"
                value={formData.birthDate}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, birthDate: e.target.value })}
                icon={Calendar}
              />
            </div>

            <ModernTextarea
              label="Observações"
              value={formData.notes}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setFormData({ ...formData, notes: e.target.value })}
              placeholder="Observações sobre o aluno..."
              rows={3}
              icon={FileText}
            />

          </form>
        </FormCard>
      </AdminPageLayout>
    );
  }

  if (mode === 'edit' && selectedStudent) {
    return (
      <AdminPageLayout
        title="Editar Aluno"
        description={`Edite as informações do aluno ${selectedStudent.name}`}
        icon={Users}
        backUrl="/admin/students"
        actionButton={{
          label: selectedStudent.faceDescriptor ? 'Atualizar Face' : 'Cadastrar Face',
          onClick: () => setFaceRegistrationOpen(true),
          icon: Camera
        }}
      >
        <FormCard 
          title="Informações do Aluno" 
          icon={User}
          headerAction={
            <AutoSaveIndicator
              isAutoSaving={autoSaveState.isAutoSaving}
              lastSaved={autoSaveState.lastSaved}
              hasUnsavedChanges={autoSaveState.hasUnsavedChanges}
              error={autoSaveState.error}
            />
          }
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 xl:grid-cols-3">
              <ModernInput
                label="Nome"
                value={formData.name}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Nome completo do aluno"
                icon={User}
                required
                className="md:col-span-2 xl:col-span-1"
              />

              <ModernSelect
                label="Status"
                value={formData.status}
                onValueChange={(value) => setFormData({ ...formData, status: value })}
                icon={Activity}
                options={[
                  { value: 'active', label: 'Ativo' },
                  { value: 'inactive', label: 'Inativo' },
                  { value: 'suspended', label: 'Suspenso' }
                ]}
              />

              <ModernInput
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, email: e.target.value })}
                placeholder="<EMAIL>"
                icon={Mail}
              />

              <ModernInput
                label="Telefone"
                value={formData.phone}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, phone: e.target.value })}
                placeholder="(11) 99999-9999"
                icon={Phone}
              />

              <ModernInput
                label="Data de Nascimento"
                type="date"
                value={formData.birthDate}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, birthDate: e.target.value })}
                icon={Calendar}
              />
            </div>

            <ModernTextarea
              label="Observações"
              value={formData.notes}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setFormData({ ...formData, notes: e.target.value })}
              placeholder="Observações sobre o aluno..."
              rows={3}
              icon={FileText}
            />

          </form>
        </FormCard>

        {/* Face Recognition Info Card */}
        <FormCard title="Reconhecimento Facial" icon={Camera}>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">
                  {selectedStudent.faceDescriptor
                    ? `Dados faciais cadastrados em ${new Date(selectedStudent.faceDataUpdatedAt!).toLocaleDateString('pt-BR')}`
                    : 'Cadastre os dados faciais para reconhecimento automático na presença'
                  }
                </p>
              </div>
              <div className="flex items-center space-x-2">
                {selectedStudent.faceDescriptor ? (
                  <div className="flex items-center text-green-600 text-sm">
                    <div className="w-2 h-2 bg-green-600 rounded-full mr-2"></div>
                    Cadastrado
                  </div>
                ) : (
                  <div className="flex items-center text-gray-500 text-sm">
                    <div className="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
                    Não cadastrado
                  </div>
                )}
              </div>
            </div>

            {/* Face Photo Preview */}
            {selectedStudent.photoUrl && (
              <div className="text-center">
                <h5 className="text-sm font-medium text-gray-700 mb-2">Foto Cadastrada</h5>
                <img
                  src={selectedStudent.photoUrl}
                  alt={`Foto facial de ${selectedStudent.name}`}
                  className="w-32 h-32 object-cover rounded-lg border border-gray-300 mx-auto"
                />
              </div>
            )}

            <div className="grid gap-3 md:grid-cols-2">
              <ModernButton
                onClick={() => setFaceRegistrationOpen(true)}
                variant="outline"
                icon={Camera}
                className="w-full"
              >
                {selectedStudent.faceDescriptor ? 'Atualizar Dados' : 'Cadastrar Dados'}
              </ModernButton>
              
              {selectedStudent.faceDescriptor && (
                <ModernButton
                  onClick={() => handleRemoveFaceData(selectedStudent)}
                  variant="outline"
                  className="w-full border-red-300 text-red-600 hover:bg-red-50"
                >
                  Remover Dados
                </ModernButton>
              )}
            </div>
          </div>
        </FormCard>

        {/* Student Enrollments Section */}
        <StudentEnrollmentsView
          studentId={selectedStudent.id}
          studentName={selectedStudent.name}
        />

        {/* Face Registration Modal */}
        <FaceRegistration
          student={selectedStudent}
          open={faceRegistrationOpen}
          onOpenChange={setFaceRegistrationOpen}
          onRegistrationComplete={handleFaceRegistrationComplete}
        />
      </AdminPageLayout>
    );
  }

  // Default list view
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="w-full max-w-[1600px] mx-auto">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-2xl p-8 text-white shadow-xl mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button onClick={() => router.push('/')} variant="outline" size="sm" className="bg-white/20 text-white border-white/30 hover:bg-white/30">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar
              </Button>
              <div>
                <h1 className="text-3xl font-bold mb-2 flex items-center">
                  <Users className="w-8 h-8 mr-3" />
                  Gerenciar Alunos
                </h1>
                <p className="text-white/90">
                  Administre informações dos alunos e dados de reconhecimento facial
                </p>
              </div>
            </div>
            <div className="hidden md:block">
              <Button 
                onClick={() => navigateToMode('new')}
                className="bg-white/20 text-white border-white/30 hover:bg-white/30 backdrop-blur-sm"
                size="lg"
              >
                <Plus className="w-5 h-5 mr-2" />
                Novo Aluno
              </Button>
            </div>
          </div>
          {/* Mobile button */}
          <div className="md:hidden mt-4">
            <Button 
              onClick={() => navigateToMode('new')}
              className="bg-white/20 text-white border-white/30 hover:bg-white/30 backdrop-blur-sm w-full"
            >
              <Plus className="w-5 h-5 mr-2" />
              Novo Aluno
            </Button>
          </div>
        </div>

        {/* Search Section */}
        <Card className="mb-6 shadow-lg border-0">
          <CardContent className="pt-6">
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Buscar alunos por nome, email ou telefone..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  className="border-2 border-gray-200 focus:border-[#667eea] focus:ring-4 focus:ring-[#667eea]/20 transition-all duration-300"
                />
              </div>
              <Button 
                onClick={handleSearch}
                className="bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:from-[#5a6fd8] hover:to-[#6b4190] text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
              >
                <Search className="w-4 h-4 mr-2" />
                Buscar
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Students Table */}
        <Card className="shadow-xl border-0">
          <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
            <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
              <Users className="w-6 h-6 mr-2 text-[#667eea]" />
              Alunos Cadastrados
            </CardTitle>
            <CardDescription className="text-lg">
              {students.length} aluno{students.length !== 1 ? 's' : ''} encontrado{students.length !== 1 ? 's' : ''}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading && mode === 'list' ? (
              <div className="text-center py-8">
                <Loader2 className="mx-auto animate-spin" size={32} />
                <p className="mt-4 text-muted-foreground">Carregando alunos...</p>
              </div>
            ) : students.length === 0 ? (
              <div className="text-center py-8">
                <Users className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-2 text-sm font-semibold text-foreground">
                  Nenhum aluno encontrado
                </h3>
                <p className="mt-1 text-sm text-muted-foreground">
                  {searchTerm ? 'Tente uma busca diferente.' : 'Comece cadastrando um novo aluno.'}
                </p>
                {!searchTerm && (
                  <Button onClick={() => navigateToMode('new')} className="mt-4">
                    <Plus className="w-4 h-4 mr-2" />
                    Cadastrar Primeiro Aluno
                  </Button>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Contato</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Reconhecimento</TableHead>
                      <TableHead>Matrículas</TableHead>
                      <TableHead>Presenças</TableHead>
                      <TableHead>Cadastrado em</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {students.map((student) => (
                      <TableRow key={student.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{student.name}</div>
                            {student.birthDate && (
                              <div className="text-sm text-muted-foreground">
                                Nascimento: {formatDate(student.birthDate)}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {student.email && (
                              <div className="flex items-center text-sm">
                                <Mail className="w-3 h-3 mr-1" />
                                {student.email}
                              </div>
                            )}
                            {student.phone && (
                              <div className="flex items-center text-sm">
                                <Phone className="w-3 h-3 mr-1" />
                                {student.phone}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getStatusBadgeVariant(student.status)}>
                            {getStatusLabel(student.status)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {student.faceDescriptor ? (
                              <div className="flex items-center">
                                {student.photoUrl && (
                                  <img
                                    src={student.photoUrl}
                                    alt={`Foto de ${student.name}`}
                                    className="w-8 h-8 rounded-full object-cover border mr-2"
                                  />
                                )}
                                <Badge variant="success" className="text-xs">
                                  <Camera className="w-3 h-3 mr-1" />
                                  Cadastrado
                                </Badge>
                              </div>
                            ) : (
                              <Badge variant="secondary" className="text-xs">
                                Não cadastrado
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div>{student._count?.enrollments || 0} curso{(student._count?.enrollments || 0) !== 1 ? 's' : ''}</div>
                            {student.enrollments && student.enrollments.length > 0 && (
                              <div className="text-muted-foreground">
                                {student.enrollments.slice(0, 2).map(e => e.course.name).join(', ')}
                                {student.enrollments.length > 2 && '...'}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>{student._count?.attendance || 0}</TableCell>
                        <TableCell>{formatDate(student.registrationDate)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              onClick={() => navigateToMode('edit', student.id)}
                              variant="outline"
                              size="sm"
                              title="Editar"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              onClick={() => handleDelete(student.id, student.name)}
                              variant="outline"
                              size="sm"
                              className="text-destructive hover:text-destructive"
                              title="Excluir"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <ConfirmationDialog />

      {/* Face Registration Modal */}
      {selectedStudent && (
        <FaceRegistration
          student={selectedStudent}
          open={faceRegistrationOpen}
          onOpenChange={setFaceRegistrationOpen}
          onRegistrationComplete={handleFaceRegistrationComplete}
        />
      )}
    </div>
  );
}

export default function StudentsPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">Carregando...</p>
        </div>
      </div>
    }>
      <StudentsPageContent />
    </Suspense>
  );
}